{"WindowsLicense": {"PARTIAL_KEY": "3V66T", "WindowsBuild": "26100", "LicenseFamily": "Professional", "OA3X_KEY": "VK7JG-NPHTM-C97JM-9MPGT-3V66T", "ActivationStatus": "Activated", "LICENSE_TYPE": "Retail", "WindowsVersion": "Microsoft Windows 11 Pro", "PRODUCT_KEY": "YNTHB-PM6G4-MFJG9-RT2T8-XW3FC"}, "ComputerName": "PDINOTE-01", "SystemInfo": {"ComputerName": "PDINOTE-01", "Model": "82DJ", "TotalPhysicalMemoryGB": 7.8, "Motherboard": {"Manufacturer": "LENOVO", "Product": "LNVNB161216", "Version": "SDK0J40679 WIN", "SerialNumber": "PE07FLAV"}, "Memory": [{"Manufacturer": "Smart Brazil", "SerialNumber": "028A2A84", "CapacityGB": 4.0, "Speed": 2667, "PartNumber": "SMS4TDC3C0K0446SCG  "}, {"Manufacturer": "Smart Brazil", "SerialNumber": "00000000", "CapacityGB": 4.0, "Speed": 2667, "PartNumber": "SMS4TDC3C0K0446SCG  "}], "Storage": [{"Model": "SSSTC CL1-4D256", "MediaType": "Fixed hard disk media", "InterfaceType": "SCSI", "SizeGB": 238.47, "SerialNumber": "38F6_0156_3082_9753."}], "BIOS": {"Manufacturer": "LENOVO", "SerialNumber": "PE07FLAV", "Version": "LENOVO - 1", "ReleaseDate": "2022-05-23T21:00:00-03:00"}, "Network": [{"Name": "Intel(R) Wireless-AC 9462", "Speed": 200000000, "Manufacturer": "Intel Corporation", "MACAddress": "94:E2:3C:19:B5:22"}], "Graphics": [{"Name": "Intel(R) UHD Graphics", "DriverDate": "2021-05-31T21:00:00-03:00", "DriverVersion": "27.20.100.9664", "AdapterRAMMB": 1024.0}], "Processor": {"Name": "Intel(R) Core(TM) i5-1035G1 CPU @ 1.00GHz", "MaxClockSpeed": 1190, "LogicalProcessors": 8, "Cores": 4, "Architecture": 9}, "Manufacturer": "LENOVO"}, "OfficeLicense": {"ActiveLicenses": [{"EmailAddress": "<EMAIL>", "Status": "Active", "Method": "LicensingNext", "CID": "5363404ae0cf3c07", "LicenseID": "5363404ae0cf3c07", "OfficeVersion": "16.0"}], "DetectionMethod": "Registry Analysis", "RunningProcesses": [{"WindowTitle": "Excel", "Id": 15924, "StartTime": "2025-07-31 12:42:50", "ProcessName": "EXCEL"}, {"WindowTitle": "Chat | <PERSON><PERSON> <PERSON> | Microsoft Teams", "Id": 2000, "StartTime": "2025-07-31 09:01:03", "ProcessName": "ms-teams"}, {"WindowTitle": "", "Id": 19932, "StartTime": "2025-07-31 09:01:52", "ProcessName": "ms-teams"}], "ClickToRunInfo": {"ProductIds": "O365HomePremRetail", "Platform": "x64", "Version": "16.0.18925.20184", "InstallType": "Click-to-Run", "Channel": "http://officecdn.microsoft.com/pr/492350f6-3a01-4f97-b9c0-c7c6ddf67d60", "ClientCulture": "pt-BR", "UpdateChannel": "http://officecdn.microsoft.com/pr/492350f6-3a01-4f97-b9c0-c7c6ddf67d60"}, "Status": "Active"}, "InstalledApplications": [{"InstallDate": "N/A", "Publisher": "<PERSON>", "UninstallString": "\"C:\\Program Files\\7-Zip\\Uninstall.exe\"", "InstallLocation": "C:\\Program Files\\7-Zip\\", "Version": "22.00", "Name": "7-Zip 22.00 (x64)", "EstimatedSizeMB": 5.45}, {"InstallDate": "20250103", "Publisher": "Famatech", "UninstallString": "MsiExec.exe /X{DF846D58-3E49-4734-AA93-8807D30A1973}", "InstallLocation": "C:\\Program Files (x86)\\Advanced IP Scanner\\", "Version": "2.5.4594.1", "Name": "Advanced IP Scanner 2.5.1", "EstimatedSizeMB": 41.36}, {"InstallDate": "20241022", "Publisher": "<PERSON><PERSON><PERSON><PERSON>", "UninstallString": "MsiExec.exe /X{D3E42B4A-727D-4323-A964-9304C04DDFB3}", "InstallLocation": "N/A", "Version": "***********", "Name": "<PERSON><PERSON>", "EstimatedSizeMB": 46.93}, {"InstallDate": "N/A", "Publisher": "AnyDesk Software GmbH", "UninstallString": "\"C:\\Program Files (x86)\\AnyDesk\\AnyDesk.exe\" --uninstall", "InstallLocation": "\"C:\\Program Files (x86)\\AnyDesk\"", "Version": "ad 9.0.7", "Name": "AnyDesk", "EstimatedSizeMB": 2.0}, {"InstallDate": "20250509", "Publisher": "Amazon Web Services", "UninstallString": "MsiExec.exe /I{0A5277D9-326E-4A57-BFF9-79D5BB8E972B}", "InstallLocation": "N/A", "Version": "*********", "Name": "AWS Command Line Interface v2", "EstimatedSizeMB": 171.36}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{B53D07F4-3041-41E0-BEB3-C713426D3C5F}", "InstallLocation": "N/A", "Version": "15.0.1300.359", "Name": "Biblioteca de Autenticação do Active Directory para SQL Server", "EstimatedSizeMB": 0.07}, {"InstallDate": "N/A", "Publisher": "now.gg, Inc.", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\bluestacks-services\\Uninstall BlueStacksServices.exe\" /currentuser", "InstallLocation": "N/A", "Version": "3.0.9", "Name": "BlueStacks Services", "EstimatedSizeMB": 319.24}, {"InstallDate": "20250730", "Publisher": "Autores do Brave", "UninstallString": "\"C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\138.1.80.125\\Installer\\setup.exe\" --uninstall --system-level", "InstallLocation": "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application", "Version": "138.1.80.125", "Name": "Brave", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250224", "Publisher": "Cisco Systems, Inc.", "UninstallString": "\"C:\\Program Files\\Cisco Packet Tracer 8.2.2\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\Cisco Packet Tracer 8.2.2\\", "Version": "8.2.2.400", "Name": "Cisco Packet Tracer 8.2.2 64Bit", "EstimatedSizeMB": 566.08}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{06FF06FA-A753-47E2-9E5A-F2037B7FE4AC}", "InstallLocation": "N/A", "Version": "4.8.09037", "Name": "ClickOnce Bootstrapper Package for Microsoft .NET Framework", "EstimatedSizeMB": 8.29}, {"InstallDate": "20250529", "Publisher": "CPUID, Inc.", "UninstallString": "\"C:\\Program Files\\CPUID\\CPU-Z\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\CPUID\\CPU-Z\\", "Version": "2.15", "Name": "CPUID CPU-Z 2.15", "EstimatedSizeMB": 7.86}, {"InstallDate": "20250715", "Publisher": "Anysphere", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\unins000.exe\"", "InstallLocation": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\", "Version": "1.2.4", "Name": "<PERSON><PERSON><PERSON> (User)", "EstimatedSizeMB": 464.03}, {"InstallDate": "20211201", "Publisher": "Microsoft", "UninstallString": "MsiExec.exe /I{D066B018-448B-40C5-9034-259BBCC49351}", "InstallLocation": "N/A", "Version": "4.6.2.0", "Name": "DefaultPackMSI", "EstimatedSizeMB": 2.96}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{0E5296FF-C952-4756-8C85-6E6CC9F73A20}", "InstallLocation": "N/A", "Version": "17.12.35318", "Name": "DiagnosticsHub_CollectionService", "EstimatedSizeMB": 1.19}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{331567C2-6A4D-44EF-9F99-940438FECB2F}", "InstallLocation": "N/A", "Version": "********", "Name": "Driver do Microsoft OLE DB para SQL Server", "EstimatedSizeMB": 8.81}, {"InstallDate": "20250522", "Publisher": "Eclipse Adoptium", "UninstallString": "MsiExec.exe /I{7F8D17DD-98B6-4A5D-87E7-E0B732691B8B}", "InstallLocation": "C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\", "Version": "********", "Name": "Eclipse Temurin JDK with Hotspot 21.0.7+6 (x64)", "EstimatedSizeMB": 328.41}, {"InstallDate": "N/A", "Publisher": "Intelbras", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\FastConfig\\Uninstall FastConfig.exe\" /currentuser", "InstallLocation": "N/A", "Version": "2.0.0", "Name": "FastConfig 2.0.0", "EstimatedSizeMB": 270.24}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" uninstall --installPath \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\"", "InstallLocation": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools", "Version": "16.11.43", "Name": "Ferramentas de Build do Visual Studio 2019 (2)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "N/A", "Publisher": "<PERSON>", "UninstallString": "\"C:\\Program Files\\FileZilla FTP Client\\uninstall.exe\"", "InstallLocation": "C:\\Program Files\\FileZilla FTP Client", "Version": "3.68.1", "Name": "FileZilla 3.68.1", "EstimatedSizeMB": 43.29}, {"InstallDate": "20250317", "Publisher": "The Git Development Community", "UninstallString": "\"C:\\Program Files\\Git\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\Git\\", "Version": "2.48.1", "Name": "Git", "EstimatedSizeMB": 347.73}, {"InstallDate": "20250318", "Publisher": "GitHub, Inc.", "UninstallString": "\"C:\\Program Files\\Git LFS\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\Git LFS\\", "Version": "3.6.1", "Name": "Git LFS version 3.6.1", "EstimatedSizeMB": 14.57}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{4730D275-FA69-4D19-95C8-DA9F64749330}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "Gravador VSS da Microsoft para SQL Server 2022", "EstimatedSizeMB": 2.46}, {"InstallDate": "20220215", "Publisher": "<PERSON><PERSON><PERSON>", "UninstallString": "\"C:\\Program Files\\HeidiSQL\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\HeidiSQL\\", "Version": "11.3", "Name": "HeidiSQL 11.3.0.6295", "EstimatedSizeMB": 56.65}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{56070933-B0D1-493F-8C12-4F7E83CA3071}", "InstallLocation": "N/A", "Version": "10.0.06614", "Name": "IIS 10.0 Express", "EstimatedSizeMB": 57.24}, {"InstallDate": "N/A", "Publisher": "N/A", "UninstallString": "C:\\WINDOWS\\system32\\sdbinst.exe -u \"C:\\Windows\\AppPatch\\CustomSDB\\{08274920-8908-45c2-9258-8ad67ff77b09}.sdb\"", "InstallLocation": "N/A", "Version": "N/A", "Name": "IIS Express Application Compatibility Database for x64", "EstimatedSizeMB": "N/A"}, {"InstallDate": "N/A", "Publisher": "N/A", "UninstallString": "C:\\WINDOWS\\system32\\sdbinst.exe -u \"C:\\Windows\\AppPatch\\CustomSDB\\{ad846bae-d44b-4722-abad-f7420e08bcd9}.sdb\"", "InstallLocation": "N/A", "Version": "N/A", "Name": "IIS Express Application Compatibility Database for x86", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20221104", "Publisher": "ImageMagick Studio LLC", "UninstallString": "\"C:\\Program Files\\ImageMagick-7.1.0-Q16-HDRI\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\ImageMagick-7.1.0-Q16-HDRI\\", "Version": "7.1.0.51", "Name": "ImageMagick 7.1.0-51 Q16-HDRI (64-bit) (2022-10-16)", "EstimatedSizeMB": 115.61}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{FB1540A1-4655-4908-8041-54B6C344F692}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "Instalação do Microsoft SQL Server 2022 (em inglês)", "EstimatedSizeMB": 47.92}, {"InstallDate": "20250116", "Publisher": "Intelbras", "UninstallString": "\"C:\\Program Files\\Intelbras\\SIMNext\\unins001.exe\"", "InstallLocation": "C:\\Program Files\\Intelbras\\SIMNext\\", "Version": "1.25.1", "Name": "Intelbras SIM Next 1.25.1", "EstimatedSizeMB": 460.93}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{F8B9E8C8-61E8-4E9E-879D-F3F498AD0230}", "InstallLocation": "N/A", "Version": "15.0.21225.01", "Name": "IntelliTraceProfilerProxy", "EstimatedSizeMB": 0.01}, {"InstallDate": "N/A", "Publisher": "Intelbras S/A", "UninstallString": "C:\\Program Files (x86)\\IP Utility\\uninst.exe", "InstallLocation": "N/A", "Version": "1.0.1", "Name": "IP Utility 1.0.1", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250103", "Publisher": "Oracle Corporation", "UninstallString": "MsiExec.exe /I{71024AE4-039E-4CA4-87B4-2F64180431F0}", "InstallLocation": "C:\\Program Files\\Java\\jre1.8.0_431\\", "Version": "8.0.4310.10", "Name": "Java 8 Update 431 (64-bit)", "EstimatedSizeMB": 212.08}, {"InstallDate": "20250103", "Publisher": "Oracle Corporation", "UninstallString": "N/A", "InstallLocation": "N/A", "Version": "2.8.431.10", "Name": "Java Auto Updater", "EstimatedSizeMB": 2.38}, {"InstallDate": "20230830", "Publisher": "Oracle", "UninstallString": "MsiExec.exe /I{32A3A4F4-B792-11D6-A78A-00B0D0170760}", "InstallLocation": "C:\\Program Files (x86)\\Java\\jdk1.7.0_76\\", "Version": "1.7.0.760", "Name": "Java SE Development Kit 7 Update 76", "EstimatedSizeMB": 223.54}, {"InstallDate": "qui dez 12 12:49:26 2024", "Publisher": "JBL", "UninstallString": "C:\\Program Files\\JBL\\QuantumENGINE\\QuantumUninstaller.exe", "InstallLocation": "C:\\Program Files\\JBL\\QuantumENGINE", "Version": "1.19.0.2025", "Name": "JBL QuantumENGINE", "EstimatedSizeMB": 258.37}, {"InstallDate": "20241112", "Publisher": "KLCP", "UninstallString": "\"C:\\Program Files (x86)\\K-Lite Codec Pack\\unins000.exe\"", "InstallLocation": "C:\\Program Files (x86)\\K-Lite Codec Pack\\", "Version": "18.6.5", "Name": "K-Lite Codec Pack 18.6.5 Standard", "EstimatedSizeMB": 137.16}, {"InstallDate": "N/A", "Publisher": "N/A", "UninstallString": "N/A", "InstallLocation": "C:\\Program Files (x86)\\Kaspersky Lab\\KES.12.8.0\\", "Version": "11.20.8.505", "Name": "Kaspersky Endpoint Security for Windows", "EstimatedSizeMB": "N/A"}, {"InstallDate": "N/A", "Publisher": "<PERSON><PERSON><PERSON>", "UninstallString": "MsiExec.exe /I{BCF4CF24-88AB-45E1-A6E6-40C8278A70C5} /l*v \"C:\\Windows\\Temp\\$klnagent-uninstall.log\" ", "InstallLocation": "C:\\Program Files (x86)\\Kaspersky Lab\\NetworkAgent\\", "Version": "15.1.0.11795", "Name": "Kaspersky Security Center Network Agent", "EstimatedSizeMB": 175.98}, {"InstallDate": "20250220", "Publisher": "<PERSON><PERSON><PERSON>", "UninstallString": "\"C:\\Program Files\\KeePass Password Safe 2\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\KeePass Password Safe 2\\", "Version": "2.57.1", "Name": "KeePass Password Safe 2.57.1", "EstimatedSizeMB": 16.91}, {"InstallDate": "N/A", "Publisher": "SolarWinds", "UninstallString": "\"C:\\Program Files (x86)\\Syslogd\\uninst-Syslogd.exe\"", "InstallLocation": "C:\\Program Files (x86)\\Syslogd", "Version": "9.8.3  (Standard Edition)", "Name": "Kiwi Syslog Server 9.8.3  (Standard Edition)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20241216", "Publisher": "Lenovo", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Lenovo\\Lenovo Service Bridge\\unins000.exe\"", "InstallLocation": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Lenovo\\Lenovo Service Bridge\\", "Version": "5.0.2.18", "Name": "Lenovo Service Bridge", "EstimatedSizeMB": 20.6}, {"InstallDate": "20240814", "Publisher": "Lenovo", "UninstallString": "\"C:\\Program Files (x86)\\Lenovo\\System Update\\unins000.exe\"", "InstallLocation": "C:\\Program Files (x86)\\Lenovo\\System Update\\", "Version": "**********", "Name": "Lenovo System Update", "EstimatedSizeMB": 49.9}, {"InstallDate": "20220722", "Publisher": "<PERSON>ll<PERSON><PERSON>", "UninstallString": "\"C:\\Program Files (x86)\\Skillbrains\\lightshot\\unins000.exe\"", "InstallLocation": "C:\\Program Files (x86)\\Skillbrains\\lightshot\\", "Version": "*******", "Name": "Lightshot-*******", "EstimatedSizeMB": 3.98}, {"InstallDate": "N/A", "Publisher": "www.microsip.org", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\MicroSIP\\Uninstall.exe\"", "InstallLocation": "N/A", "Version": "3.21.6", "Name": "MicroSIP", "EstimatedSizeMB": 19.33}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{DB3E687F-E697-4D2F-9C99-B36B2AC84B73}", "InstallLocation": "N/A", "Version": "12.23.29928", "Name": "Microsoft .NET Core 3.1 Templates 5.0.218 (x64)", "EstimatedSizeMB": 4.75}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{1BEBAFC9-CECD-4075-AAC2-754731645855}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core AppHost Pack - 3.1.29 (x64_arm)", "EstimatedSizeMB": 0.61}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{845B06D3-F90F-4CD7-8A33-A225A6C690DF}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core AppHost Pack - 3.1.29 (x64_arm64)", "EstimatedSizeMB": 0.82}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{3E3F0AD9-CC73-4BCC-B554-030EF9EA6021}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core AppHost Pack - 3.1.29 (x64_x86)", "EstimatedSizeMB": 0.69}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{3E6E5DA3-3DF2-44F4-AB68-239F13E29022}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core AppHost Pack - 3.1.29 (x64)", "EstimatedSizeMB": 0.88}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{2AA2F795-2ED6-4E8B-85CE-EBD5F0BD744D}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core Runtime - 3.1.29 (x64)", "EstimatedSizeMB": 65.66}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{F2DD00EC-56E6-43AC-8A15-6EE585F90D1C}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft .NET Core Runtime - 3.1.29 (x86)", "EstimatedSizeMB": 58.47}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{31EDE1E7-C855-4633-9D73-56F566136567}", "InstallLocation": "N/A", "Version": "24.64.28315", "Name": "Microsoft .NET Core Targeting Pack - 3.1.0 (x64)", "EstimatedSizeMB": 24.4}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{CFEF48A8-BFB8-3EAC-8BA5-DE4F8AA267CE}", "InstallLocation": "N/A", "Version": "4.0.30319", "Name": "Microsoft .NET Framework 4 Multi-Targeting Pack", "EstimatedSizeMB": 83.45}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{56E962F0-4FB0-3C67-88DB-9EAA6EEFC493}", "InstallLocation": "N/A", "Version": "4.5.50710", "Name": "Microsoft .NET Framework 4.5 Multi-Targeting Pack", "EstimatedSizeMB": 41.86}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{6A0C6700-EA93-372C-8871-DCCF13D160A4}", "InstallLocation": "N/A", "Version": "4.5.50932", "Name": "Microsoft .NET Framework 4.5.1 Multi-Targeting Pack", "EstimatedSizeMB": 49.4}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{B941AFB4-8851-33A1-9E72-0C33D463C41C}", "InstallLocation": "N/A", "Version": "4.5.51209", "Name": "Microsoft .NET Framework 4.5.2 Multi-Targeting Pack", "EstimatedSizeMB": 49.47}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{2CC6A4A7-AAC2-46C9-9DBB-3727B5954F65}", "InstallLocation": "N/A", "Version": "4.6.00081", "Name": "Microsoft .NET Framework 4.6 Targeting Pack", "EstimatedSizeMB": 40.39}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{1784A8CD-F7FE-47E2-A87D-1F31E7242D0D}", "InstallLocation": "N/A", "Version": "4.7.03062", "Name": "Microsoft .NET Framework 4.7.2 Targeting Pack", "EstimatedSizeMB": 42.34}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{949C0535-171C-480F-9CF4-D25C9E60FE88}", "InstallLocation": "N/A", "Version": "4.8.03928", "Name": "Microsoft .NET Framework 4.8 SDK", "EstimatedSizeMB": 20.57}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{C30ABA3F-32C0-43D1-B3B8-9AEFD58A15D9}", "InstallLocation": "N/A", "Version": "48.39.47157", "Name": "Microsoft .NET Host - 6.0.9 (x64)", "EstimatedSizeMB": 0.43}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{4C903F19-B4C3-4D0C-8CC9-D444C511AF1C}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Host - 8.0.15 (x64)", "EstimatedSizeMB": 0.47}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{CCD2C34B-888A-4C37-B640-A801B42F3D5D}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Host - 8.0.15 (x86)", "EstimatedSizeMB": 0.43}, {"InstallDate": "20220425", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{51701D62-C986-4508-B423-5EFE6FF708B7}", "InstallLocation": "N/A", "Version": "48.19.39076", "Name": "Microsoft .NET Host FX Resolver - 6.0.4 (x64)", "EstimatedSizeMB": 0.34}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{FD10B803-97FD-4867-9753-8784BC35D2F8}", "InstallLocation": "N/A", "Version": "48.39.47157", "Name": "Microsoft .NET Host FX Resolver - 6.0.9 (x64)", "EstimatedSizeMB": 0.34}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{84E57A75-AA9E-4DF6-ABDD-2BBB466C197D}", "InstallLocation": "N/A", "Version": "48.39.47157", "Name": "Microsoft .NET Host FX Resolver - 6.0.9 (x86)", "EstimatedSizeMB": 0.28}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{11CCC9F6-77AA-4421-9EAC-BAEC36D96817}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Host FX Resolver - 8.0.15 (x64)", "EstimatedSizeMB": 0.32}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{8FAD5F55-28B8-4475-9AFE-9416326E5367}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Host FX Resolver - 8.0.15 (x86)", "EstimatedSizeMB": 0.26}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{78a41aea-a74d-484b-baf5-dc41cd70dc88}\\dotnet-runtime-6.0.4-win-x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "6.0.4.31114", "Name": "Microsoft .NET Runtime - 6.0.4 (x64)", "EstimatedSizeMB": 95.85}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{0B4F742D-2D47-4E95-B756-402822D31C48}", "InstallLocation": "N/A", "Version": "48.39.47157", "Name": "Microsoft .NET Runtime - 6.0.9 (x64)", "EstimatedSizeMB": 67.93}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{096A34D3-C29C-4464-9B91-5EF8EFC6A0BD}", "InstallLocation": "N/A", "Version": "48.39.47157", "Name": "Microsoft .NET Runtime - 6.0.9 (x86)", "EstimatedSizeMB": 62.07}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{8731E6E3-AF96-4515-ACEC-DBFB3DF55292}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Runtime - 8.0.15 (x64)", "EstimatedSizeMB": 70.23}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{5BC8AE9F-2277-483E-BF7E-C70EF841641D}", "InstallLocation": "N/A", "Version": "64.60.31149", "Name": "Microsoft .NET Runtime - 8.0.15 (x86)", "EstimatedSizeMB": 64.76}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{B03BB0D6-59B4-4E53-819D-BCF33A0759CB}", "InstallLocation": "N/A", "Version": "5.2.1822.41224", "Name": "Microsoft .NET SDK 5.0.218 (x64) from Visual Studio", "EstimatedSizeMB": 0.18}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{A7036CFB-B403-4598-85FF-D397ABB88173}", "InstallLocation": "N/A", "Version": "24.0.28113", "Name": "Microsoft .NET Standard Targeting Pack - 2.1.0 (x64)", "EstimatedSizeMB": 19.43}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{4872ED9E-9F8C-4A8F-923C-A9BB2AA0F487}", "InstallLocation": "N/A", "Version": "20.5.46312", "Name": "Microsoft .NET Toolset 5.0.218 (x64)", "EstimatedSizeMB": 216.68}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\ClickToRun\\OfficeClickToRun.exe\" scenario=install scenariosubtype=ARP sourcetype=None productstoremove=O365HomePremRetail.16_pt-br_x-none culture=pt-br version.16=16.0", "InstallLocation": "C:\\Program Files\\Microsoft Office", "Version": "16.0.18925.20184", "Name": "Microsoft 365 - pt-br", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{FEA48357-CE2F-3ED0-B2A0-8548BEC6F111}", "InstallLocation": "N/A", "Version": "3.1.10.20520", "Name": "Microsoft ASP.NET Core 3.1.10 Targeting Pack (x64)", "EstimatedSizeMB": 11.45}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{48CE0B6F-1D27-3FCD-A180-43E8FF731B8A}", "InstallLocation": "N/A", "Version": "3.1.29.22418", "Name": "Microsoft ASP.NET Core 3.1.29 Shared Framework (x64)", "EstimatedSizeMB": 19.08}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{A50E73C7-2BAD-3612-933C-379ECC53CCAA}", "InstallLocation": "N/A", "Version": "3.1.29.22418", "Name": "Microsoft ASP.NET Core 3.1.29 Shared Framework (x86)", "EstimatedSizeMB": 17.25}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{4A56B2C5-94A0-35BE-9069-AD1902B2DB1C}", "InstallLocation": "N/A", "Version": "6.0.9.22419", "Name": "Microsoft ASP.NET Core 6.0.9 Shared Framework (x64)", "EstimatedSizeMB": 22.09}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{898EF221-8A33-33BD-93CC-8185E8D59F3F}", "InstallLocation": "N/A", "Version": "6.0.9.22419", "Name": "Microsoft ASP.NET Core 6.0.9 Shared Framework (x86)", "EstimatedSizeMB": 20.34}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{FAFEE5E3-E00A-4CE8-B495-8F66A5FAB236}", "InstallLocation": "N/A", "Version": "12.2.18292.0", "Name": "Microsoft ASP.NET Core Module for IIS Express", "EstimatedSizeMB": 0.45}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{5586FE05-A6CA-4216-A8AB-89CEF8DB610F}", "InstallLocation": "N/A", "Version": "16.0.21322.0", "Name": "Microsoft ASP.NET Core Module V2 for IIS Express", "EstimatedSizeMB": 1.12}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{66D30F83-E580-3FA0-BAF7-822FF64D22CB}", "InstallLocation": "N/A", "Version": "16.9.693.2781", "Name": "Microsoft ASP.NET Diagnostic Pack for Visual Studio", "EstimatedSizeMB": 0.05}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{499DD72E-4176-377B-B358-28DCAB3832AE}", "InstallLocation": "N/A", "Version": "1.0.21125.0", "Name": "Microsoft ASP.NET Web Tools Packages 16.0 - ENU", "EstimatedSizeMB": 939.46}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{1626CED9-E559-375E-A1A1-7AE0AD98F085}", "InstallLocation": "N/A", "Version": "1.0.21125.0", "Name": "Microsoft ASP.NET Web Tools Packages 16.0 - PTB", "EstimatedSizeMB": 8.74}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{EDADFA19-7F96-4075-A4AB-2209910626C5}", "InstallLocation": "N/A", "Version": "2.9.8899.26", "Name": "Microsoft Azure Authoring Tools - v2.9.6", "EstimatedSizeMB": 12.24}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{1657ABEE-7D56-416A-B7E0-A89CC5AAD0F7}", "InstallLocation": "N/A", "Version": "2.9.8899.26", "Name": "Microsoft Azure Compute Emulator - v2.9.6", "EstimatedSizeMB": 13.68}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{C5C91AA6-3E83-430E-8B7A-6B790083F28D}", "InstallLocation": "N/A", "Version": "3.0.0127.060", "Name": "Microsoft Azure Libraries for .NET – v2.9", "EstimatedSizeMB": 67.75}, {"InstallDate": "19/09/2022", "Publisher": "Microsoft Corporation", "UninstallString": "msiexec /x{41ac2282-f083-4495-8306-2d6abc7d5ca2}", "InstallLocation": "N/A", "Version": "5.10.19227.2113", "Name": "Microsoft Azure Storage Emulator - v5.10", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{41C0DB18-1790-465E-B0DD-D9CAA35CACBE}", "InstallLocation": "N/A", "Version": "15.0.1300.359", "Name": "Microsoft Command Line Utilities 15 for SQL Server", "EstimatedSizeMB": 2.81}, {"InstallDate": "20250728", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\138.0.3351.109\\Installer\\setup.exe\" --uninstall --msedge --channel=stable --system-level --verbose-logging", "InstallLocation": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application", "Version": "138.0.3351.109", "Name": "Microsoft Edge", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250728", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.109\\Installer\\setup.exe\" --uninstall --msedgewebview --system-level --verbose-logging", "InstallLocation": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application", "Version": "138.0.3351.109", "Name": "Microsoft Edge WebView2 Runtime", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{4AFF38FF-622D-4DA1-8142-A54E2AC86793}", "InstallLocation": "N/A", "Version": "17.10.6.1", "Name": "Microsoft ODBC Driver 17 for SQL Server", "EstimatedSizeMB": 7.76}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files\\Microsoft OneDrive\\25.127.0701.0006\\OneDriveSetup.exe\"  /uninstall  /allusers ", "InstallLocation": "N/A", "Version": "25.127.0701.0006", "Name": "Microsoft OneDrive", "EstimatedSizeMB": 424.02}, {"InstallDate": "20211201", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{C17F6DEF-D34C-4B75-97E1-D81062408B4A}", "InstallLocation": "C:\\Program Files (x86)\\Microsoft\\Microsoft Search in Bing\\", "Version": "2.0.2", "Name": "Microsoft Search in Bing", "EstimatedSizeMB": 2.48}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{A418D94A-2814-49E8-A830-233E1625C568}", "InstallLocation": "N/A", "Version": "13.1.4001.0", "Name": "Microsoft SQL Server 2016 LocalDB ", "EstimatedSizeMB": 232.4}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{36E492B8-CB83-4DA5-A5D2-D99A8E8228A1}", "InstallLocation": "N/A", "Version": "15.0.4153.1", "Name": "Microsoft SQL Server 2019 LocalDB ", "EstimatedSizeMB": 259.13}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files\\Microsoft SQL Server\\160\\Setup Bootstrap\\SQL2022\\x64\\SetupARP.exe\"", "InstallLocation": "N/A", "Version": "N/A", "Name": "Microsoft SQL Server 2022 (64 bits)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{629C8FC9-3763-4C58-8264-5288AE34AFEF}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "Microsoft SQL Server 2022 RsFx Driver", "EstimatedSizeMB": 0.41}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{603A69EA-0AB6-4AED-8C26-9A09F90525CF}", "InstallLocation": "N/A", "Version": "15.0.2000.5", "Name": "Microsoft System CLR Types para SQL Server 2019", "EstimatedSizeMB": 3.79}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{86BD803B-A941-48FF-A2B6-6A9622485158}", "InstallLocation": "N/A", "Version": "15.0.1200.24", "Name": "Microsoft System CLR Types para SQL Server 2019 CTP2.2", "EstimatedSizeMB": 3.03}, {"InstallDate": "20250725", "Publisher": "Microsoft", "UninstallString": "MsiExec.exe /I{A7AB73A3-CB10-4AA5-9D38-6AEFFBDE4C91}", "InstallLocation": "N/A", "Version": "1.25.18302", "Name": "Microsoft Teams Meeting Add-in for Microsoft Office", "EstimatedSizeMB": 723.59}, {"InstallDate": "20241005", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{C6FD611E-7EFE-488C-A0E0-974C09EF6473}", "InstallLocation": "N/A", "Version": "5.72.0.0", "Name": "Microsoft Update Health Tools", "EstimatedSizeMB": 1.02}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{ca67548a-5ebe-413a-b50c-4b9ceb6d66c6}\\vcredist_x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "11.0.61030.0", "Name": "Microsoft Visual C++ 2012 Redistributable (x64) - 11.0.61030", "EstimatedSizeMB": 20.52}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{33d1fd90-4274-48a1-9bc1-97e33d9c2d6f}\\vcredist_x86.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "11.0.61030.0", "Name": "Microsoft Visual C++ 2012 Redistributable (x86) - 11.0.61030", "EstimatedSizeMB": 17.38}, {"InstallDate": "20241016", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{37B8F9C7-03FB-3253-8781-2517C99D7C00}", "InstallLocation": "N/A", "Version": "11.0.61030", "Name": "Microsoft Visual C++ 2012 x64 Additional Runtime - 11.0.61030", "EstimatedSizeMB": 11.98}, {"InstallDate": "20241016", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{CF2BEA3C-26EA-32F8-AA9B-331F7E34BA97}", "InstallLocation": "N/A", "Version": "11.0.61030", "Name": "Microsoft Visual C++ 2012 x64 Minimum Runtime - 11.0.61030", "EstimatedSizeMB": 1.95}, {"InstallDate": "20241016", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{B175520C-86A2-35A7-8619-86DC379688B9}", "InstallLocation": "N/A", "Version": "11.0.61030", "Name": "Microsoft Visual C++ 2012 x86 Additional Runtime - 11.0.61030", "EstimatedSizeMB": 9.67}, {"InstallDate": "20241016", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{BD95A8CD-1D9F-35AD-981A-3E7925026EBB}", "InstallLocation": "N/A", "Version": "11.0.61030", "Name": "Microsoft Visual C++ 2012 x86 Minimum Runtime - 11.0.61030", "EstimatedSizeMB": 1.73}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{050d4fc8-5d48-4b8f-8972-47c82c46020f}\\vcredist_x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "12.0.30501.0", "Name": "Microsoft Visual C++ 2013 Redistributable (x64) - 12.0.30501", "EstimatedSizeMB": 20.57}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{f65db027-aff3-4070-886a-0d87064aabb1}\\vcredist_x86.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "12.0.30501.0", "Name": "Microsoft Visual C++ 2013 Redistributable (x86) - 12.0.30501", "EstimatedSizeMB": 17.19}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{929FBD26-9020-399B-9A7A-751D61F0B942}", "InstallLocation": "N/A", "Version": "12.0.21005", "Name": "Microsoft Visual C++ 2013 x64 Additional Runtime - 12.0.21005", "EstimatedSizeMB": 11.51}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{A749D8E6-B613-3BE3-8F5F-045C84EBA29B}", "InstallLocation": "N/A", "Version": "12.0.21005", "Name": "Microsoft Visual C++ 2013 x64 Minimum Runtime - 12.0.21005", "EstimatedSizeMB": 0.57}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{F8CFEB22-A2E7-3971-9EDA-4B11EDEFC185}", "InstallLocation": "N/A", "Version": "12.0.21005", "Name": "Microsoft Visual C++ 2013 x86 Additional Runtime - 12.0.21005", "EstimatedSizeMB": 9.23}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{13A4EE12-23EA-3371-91EE-EFB36DDFFF3E}", "InstallLocation": "N/A", "Version": "12.0.21005", "Name": "Microsoft Visual C++ 2013 x86 Minimum Runtime - 12.0.21005", "EstimatedSizeMB": 0.42}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{d8bbe9f9-7c5b-42c6-b715-9ee898a2e515}\\VC_redist.x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "14.44.35211.0", "Name": "Microsoft Visual C++ 2015-2022 Redistributable (x64) - 14.44.35211", "EstimatedSizeMB": 20.74}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{0b5169e3-39da-4313-808e-1f9c0407f3bf}\\VC_redist.x86.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "14.44.35211.0", "Name": "Microsoft Visual C++ 2015-2022 Redistributable (x86) - 14.44.35211", "EstimatedSizeMB": 18.24}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{B2D2DB83-DEF0-4638-A634-025F645DFBDB}", "InstallLocation": "N/A", "Version": "14.29.30157", "Name": "Microsoft Visual C++ 2019 X64 Debug Runtime - 14.29.30157", "EstimatedSizeMB": 29.49}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{C45C7D61-1241-4033-BF55-3F7A99E06DCA}", "InstallLocation": "N/A", "Version": "14.29.30157", "Name": "Microsoft Visual C++ 2019 X86 Debug Runtime - 14.29.30157", "EstimatedSizeMB": 23.97}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{86AB2CC9-08BD-4643-B0F9-F82D006D72FF}", "InstallLocation": "N/A", "Version": "14.44.35211", "Name": "Microsoft Visual C++ 2022 X64 Additional Runtime - 14.44.35211", "EstimatedSizeMB": 11.61}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{43B0D101-A022-48F4-9D04-BA404CEB1D53}", "InstallLocation": "N/A", "Version": "14.44.35211", "Name": "Microsoft Visual C++ 2022 X64 Minimum Runtime - 14.44.35211", "EstimatedSizeMB": 2.35}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{C18FB403-1E88-43C8-AD8A-CED50F23DE8B}", "InstallLocation": "N/A", "Version": "14.44.35211", "Name": "Microsoft Visual C++ 2022 X86 Additional Runtime - 14.44.35211", "EstimatedSizeMB": 10.11}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{922480B5-CAEB-4B1B-AAA4-9716EFDCE26B}", "InstallLocation": "N/A", "Version": "14.44.35211", "Name": "Microsoft Visual C++ 2022 X86 Minimum Runtime - 14.44.35211", "EstimatedSizeMB": 1.98}, {"InstallDate": "20250730", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\unins000.exe\"", "InstallLocation": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\", "Version": "1.102.3", "Name": "Microsoft Visual Studio Code (User)", "EstimatedSizeMB": 416.45}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" /uninstall", "InstallLocation": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\"", "Version": "3.14.2075.50697", "Name": "Microsoft Visual Studio Installer", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{D07657AA-968C-4629-BD6C-1B52AF825EA7}", "InstallLocation": "N/A", "Version": "3.12.2140.44225", "Name": "Microsoft Visual Studio Setup Configuration", "EstimatedSizeMB": 1.67}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{49727420-70BA-4495-9405-31F8D711CB5A}", "InstallLocation": "N/A", "Version": "3.12.2140.44225", "Name": "Microsoft Visual Studio Setup WMI Provider", "EstimatedSizeMB": 4.63}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{D8202713-FF61-4234-AE5F-0CA554EDC52B}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft Windows Desktop Runtime - 3.1.29 (x64)", "EstimatedSizeMB": 83.89}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{06E11A38-C701-4AAC-8584-FE4C83350208}", "InstallLocation": "N/A", "Version": "24.116.31617", "Name": "Microsoft Windows Desktop Runtime - 3.1.29 (x86)", "EstimatedSizeMB": 77.97}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{C1CD2FC1-92E6-4DE2-89D8-6D309881856F}", "InstallLocation": "N/A", "Version": "48.39.47171", "Name": "Microsoft Windows Desktop Runtime - 6.0.9 (x64)", "EstimatedSizeMB": 86.76}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{AB3B1EE8-9C44-4154-B8CC-58E8D7B8D8FE}", "InstallLocation": "N/A", "Version": "48.39.47171", "Name": "Microsoft Windows Desktop Runtime - 6.0.9 (x86)", "EstimatedSizeMB": 81.62}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\ProgramData\\Package Cache\\{5625bb48-295c-4113-bc92-d6a69b19b04c}\\windowsdesktop-runtime-8.0.15-win-x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "8.0.15.34718", "Name": "Microsoft Windows Desktop Runtime - 8.0.15 (x64)", "EstimatedSizeMB": 217.09}, {"InstallDate": "20250509", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{81F95A7F-AD99-4C29-98B0-423B3CF41F49}", "InstallLocation": "N/A", "Version": "64.60.31203", "Name": "Microsoft Windows Desktop Runtime - 8.0.15 (x86)", "EstimatedSizeMB": 84.68}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{7519423C-A977-4160-83A2-48633600A216}", "InstallLocation": "N/A", "Version": "24.64.28315", "Name": "Microsoft Windows Desktop Targeting Pack - 3.1.0 (x64)", "EstimatedSizeMB": 25.77}, {"InstallDate": "N/A", "Publisher": "Mozilla", "UninstallString": "\"C:\\Program Files\\Mozilla Firefox\\uninstall\\helper.exe\"", "InstallLocation": "C:\\Program Files\\Mozilla Firefox", "Version": "141.0", "Name": "Mozilla Firefox (x64 pt-BR)", "EstimatedSizeMB": 306.43}, {"InstallDate": "N/A", "Publisher": "Mozilla", "UninstallString": "\"C:\\Program Files (x86)\\Mozilla Firefox\\uninstall\\helper.exe\"", "InstallLocation": "C:\\Program Files (x86)\\Mozilla Firefox", "Version": "37.0", "Name": "Mozilla Firefox 37.0 (x86 en-US)", "EstimatedSizeMB": 83.48}, {"InstallDate": "N/A", "Publisher": "Mozilla", "UninstallString": "\"C:\\Program Files (x86)\\Mozilla Maintenance Service\\uninstall.exe\"", "InstallLocation": "N/A", "Version": "141.0.0.299", "Name": "Mozilla Maintenance Service", "EstimatedSizeMB": 0.57}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{B67833D0-3229-438E-A4F0-CC651AC22C75}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "Navegador para SQL Server 2022", "EstimatedSizeMB": 11.91}, {"InstallDate": "N/A", "Publisher": "Nmap Project", "UninstallString": "\"C:\\Program Files (x86)\\Nmap\\uninstall.exe\"", "InstallLocation": "C:\\Program Files (x86)\\Nmap", "Version": "7.97", "Name": "Nmap 7.97", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250129", "Publisher": "Node.js Foundation", "UninstallString": "MsiExec.exe /I{AA0A2391-7D56-4949-A9FD-A47AF3C8A586}", "InstallLocation": "N/A", "Version": "22.13.1", "Name": "Node.js", "EstimatedSizeMB": 96.87}, {"InstallDate": "N/A", "Publisher": "Notepad++ Team", "UninstallString": "\"C:\\Program Files\\Notepad++\\uninstall.exe\"", "InstallLocation": "N/A", "Version": "8.4.8", "Name": "Notepad++ (64-bit x64)", "EstimatedSizeMB": 14.22}, {"InstallDate": "N/A", "Publisher": "Nmap Project", "UninstallString": "\"C:\\Program Files\\Npcap\\uninstall.exe\"", "InstallLocation": "C:\\Program Files\\Npcap", "Version": "1.82", "Name": "Npcap", "EstimatedSizeMB": "N/A"}, {"InstallDate": "N/A", "Publisher": "OBS Project", "UninstallString": "C:\\Program Files\\obs-studio\\uninstall.exe", "InstallLocation": "N/A", "Version": "31.0.1", "Name": "OBS Studio", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250728", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{90160000-008C-0000-1000-0000000FF1CE}", "InstallLocation": "N/A", "Version": "16.0.18925.20184", "Name": "Office 16 Click-to-Run Extensibility Component", "EstimatedSizeMB": 34.11}, {"InstallDate": "20250728", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{90160000-007E-0000-1000-0000000FF1CE}", "InstallLocation": "N/A", "Version": "16.0.18925.20184", "Name": "Office 16 Click-to-Run Licensing Component", "EstimatedSizeMB": 3.86}, {"InstallDate": "20250728", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{90160000-008C-0416-1000-0000000FF1CE}", "InstallLocation": "N/A", "Version": "16.0.18925.20184", "Name": "Office 16 Click-to-Run Localization Component", "EstimatedSizeMB": 1.54}, {"InstallDate": "20250616", "Publisher": "OpenSSL Win64 Installer Team", "UninstallString": "\"C:\\Program Files\\OpenSSL-Win64\\unins000.exe\"", "InstallLocation": "C:\\Program Files\\OpenSSL-Win64\\", "Version": "3.5.0", "Name": "OpenSSL 3.5.0 Light (64-bit)", "EstimatedSizeMB": 19.16}, {"InstallDate": "20250123", "Publisher": "OpenVPN, Inc.", "UninstallString": "MsiExec.exe /X{8274048B-FF59-47CC-802A-8A7E6325D2D5}", "InstallLocation": "N/A", "Version": "2.6.1301", "Name": "OpenVPN 2.6.13-I001 amd64", "EstimatedSizeMB": 8.58}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{4B82ABD3-B8EE-459B-AC1E-80257BFD8884}", "InstallLocation": "N/A", "Version": "4.7.03062", "Name": "Pacote de Direcionamento do Microsoft .NET Framework 4.7.2 (<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil))", "EstimatedSizeMB": 83.36}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{F347F23F-D8E6-4037-A48D-C3372617A286}", "InstallLocation": "N/A", "Version": "4.8.09037", "Name": "Pacote IntelliSense Cumulativo do Microsoft .NET Framework para Visual Studio (Português do Brasil)", "EstimatedSizeMB": 76.82}, {"InstallDate": "20250610", "Publisher": "Postman", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Postman\\Update.exe\" --uninstall", "InstallLocation": "C:\\Users\\<USER>\\AppData\\Local\\Postman", "Version": "11.49.0", "Name": "Postman x86_64 11.49.0", "EstimatedSizeMB": 133.72}, {"InstallDate": "20250519", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{EC04B98F-C372-4BC2-AC6F-8B7BA639948D}", "InstallLocation": "C:\\Users\\<USER>\\AppData\\Local\\PowerToys\\", "Version": "0.91.0", "Name": "PowerToys (Preview)", "EstimatedSizeMB": 828.22}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Package Cache\\{7ef1ec50-83db-4508-a690-be8fddebe454}\\PowerToysUserSetup-0.91.0-x64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "0.91.0", "Name": "PowerToys (Preview) x64", "EstimatedSizeMB": 1215.3}, {"InstallDate": "20220112", "Publisher": "Paessler AG", "UninstallString": "\"C:\\Program Files (x86)\\PRTG Network Monitor\\unins000.exe\"", "InstallLocation": "C:\\Program Files (x86)\\PRTG Network Monitor\\", "Version": "21", "Name": "PRTG Network Monitor", "EstimatedSizeMB": 1428.38}, {"InstallDate": "20220915", "Publisher": "<PERSON>", "UninstallString": "MsiExec.exe /X{E078C644-A120-4668-AD62-02E9FD530190}", "InstallLocation": "N/A", "Version": "0.77.0.0", "Name": "PuTTY release 0.77 (64-bit)", "EstimatedSizeMB": 4.8}, {"InstallDate": "N/A", "Publisher": "Python Software Foundation", "UninstallString": "\"C:\\Users\\<USER>\\AppData\\Local\\Package Cache\\{1abbd55d-059a-4d1e-bdf1-35bb74697f5a}\\python-3.13.1-amd64.exe\"  /uninstall", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 (64-bit)", "EstimatedSizeMB": 161.68}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{95338D2D-2A7C-4C57-ABC4-39FF8568C2FF}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Add to Path (64-bit)", "EstimatedSizeMB": 0.05}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{B7C30E07-E007-43ED-A9E1-EEDA7F57C8BC}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Core Interpreter (64-bit)", "EstimatedSizeMB": 5.87}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{FE9B3181-7FDD-4F6A-855A-305940D9A6E8}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Development Libraries (64-bit)", "EstimatedSizeMB": 2.75}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{29EEEBD6-F97B-4274-A640-FD8715025124}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Documentation (64-bit)", "EstimatedSizeMB": 61.3}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{8AFC9846-E7A8-4817-93FD-3542456A3E52}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Executables (64-bit)", "EstimatedSizeMB": 2.49}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{2BB3559A-6DFD-453E-8B7B-E6166958D099}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 pip <PERSON> (64-bit)", "EstimatedSizeMB": 0.28}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{29A3DBE6-A3D3-42C9-9338-A321F61C897A}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Standard Library (64-bit)", "EstimatedSizeMB": 23.13}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{C6718DB8-8965-4EE7-A056-1AA8F3836208}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Tcl/Tk Support (64-bit)", "EstimatedSizeMB": 13.57}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /I{7A5D8A6D-A0A9-4459-88EF-33C91DAFB0C2}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python 3.13.1 Test Suite (64-bit)", "EstimatedSizeMB": 31.89}, {"InstallDate": "20250129", "Publisher": "Python Software Foundation", "UninstallString": "MsiExec.exe /X{7102CAE5-270C-4E81-AC25-27699156D8AE}", "InstallLocation": "N/A", "Version": "3.13.1150.0", "Name": "Python Launcher", "EstimatedSizeMB": 1.5}, {"InstallDate": "20250108", "Publisher": "Famatech", "UninstallString": "MsiExec.exe /X{9F9073EA-5DCE-4B23-8A0C-C7D2C89AEADC}", "InstallLocation": "C:\\Program Files (x86)\\Radmin Viewer 3\\", "Version": "3.52.1.0000", "Name": "<PERSON><PERSON><PERSON> 3.5.2", "EstimatedSizeMB": 11.6}, {"InstallDate": "20250611", "Publisher": "SAP", "UninstallString": "MsiExec.exe /I{26ECEBA0-FCC4-42F5-9538-13FF303B22EA}", "InstallLocation": "N/A", "Version": "13.0.38.5404", "Name": "SAP Crystal Reports, version for Microsoft Visual Studio (64-bit)", "EstimatedSizeMB": 644.83}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{B08D7ADA-196F-4A57-A6F2-827CE78D86DB}", "InstallLocation": "N/A", "Version": "4.8.03761", "Name": "SDK do Microsoft .NET Framework 4.8 (<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil))", "EstimatedSizeMB": 3.53}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{7EFD8B19-A9E6-41CF-A96F-B9B6E30EC345}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Batch Parser", "EstimatedSizeMB": 0.67}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{87E5BFF7-3D50-4857-B949-0EC70F492584}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Common Files", "EstimatedSizeMB": 3.75}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{EAC54B82-7A37-4A9E-8953-474316BD40F6}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Connection Info", "EstimatedSizeMB": 0.35}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{6621C765-569C-4D46-A8E9-C69A47971357}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Database Engine Services", "EstimatedSizeMB": 439.21}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{6EFF7A19-06A9-41C2-9B3D-BD3334BF245B}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Database Engine Shared", "EstimatedSizeMB": 11.83}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{5AB77D4E-9E5F-4627-B78B-129A5EC2858A}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 DMF", "EstimatedSizeMB": 0.77}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{6F8242AA-1B25-421C-8E45-FC5978D9AA3A}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Shared Management Objects", "EstimatedSizeMB": 6.41}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{A0F7ACBA-075F-4BC7-A85A-5DC301FCEC74}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 Shared Management Objects Extensions", "EstimatedSizeMB": 2.75}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{0CEFE958-E71A-4171-9DEF-77E9234A5613}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 SQL Diagnostics", "EstimatedSizeMB": 0.53}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{EFB0090F-1AB3-4004-A8DF-CCCDA44FC803}", "InstallLocation": "N/A", "Version": "16.0.1000.6", "Name": "SQL Server 2022 XEvent", "EstimatedSizeMB": 0.28}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" uninstall --installPath \"C:\\Program Files\\Microsoft SQL Server Management Studio 21\\Release\"", "InstallLocation": "C:\\Program Files\\Microsoft SQL Server Management Studio 21\\Release", "Version": "21.3.7", "Name": "SQL Server Management Studio 21", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20211201", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{731F6BAA-A986-45A4-8936-7C3AAAAA760B}", "InstallLocation": "N/A", "Version": "1.4.0.22976", "Name": "Teams Machine-Wide Installer", "EstimatedSizeMB": 118.81}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{54BBE05F-F2AC-4403-AA5D-786BEAA645D5}", "InstallLocation": "N/A", "Version": "*******", "Name": "TypeScript SDK", "EstimatedSizeMB": 27.24}, {"InstallDate": "N/A", "Publisher": "N/A", "UninstallString": "C:\\Users\\<USER>\\Ubiquiti UniFi\\Uninstall.exe", "InstallLocation": "N/A", "Version": "N/A", "Name": "Ubiquiti UniFi (remove only)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{0460C87B-7F4C-3170-FAC9-B7A6AE5CE4E9}", "InstallLocation": "N/A", "Version": "10.0.26624", "Name": "Universal CRT Redistributable", "EstimatedSizeMB": 5.26}, {"InstallDate": "N/A", "Publisher": "Microsoft Corporation", "UninstallString": "C:\\Windows\\SysWOW64\\msiexec.exe /package {CFEF48A8-BFB8-3EAC-8BA5-DE4F8AA267CE} /uninstall {815F0BC1-7E54-300C-9ACA-C9460FDF6F78} /qb+ REBOOTPROMPT=&quot;&quot;", "InstallLocation": "N/A", "Version": "1", "Name": "Update for  (KB2504637)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20240515", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{85C69797-7336-4E83-8D97-32A7C8465A3B}", "InstallLocation": "N/A", "Version": "8.94.0.0", "Name": "Update for Windows 10 for x64-based Systems (KB5001716)", "EstimatedSizeMB": 0.82}, {"InstallDate": "N/A", "Publisher": "<PERSON><PERSON>", "UninstallString": "\"C:\\Program Files\\USBPcap\\Uninstall.exe\"", "InstallLocation": "N/A", "Version": "1.5.4.0", "Name": "USBPcap 1.5.4.0", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{56E0F628-D8BB-4FC2-A579-43F0C5366B6F}", "InstallLocation": "N/A", "Version": "14.32.31332", "Name": "vcpp_crt.redist.clickonce", "EstimatedSizeMB": 0.0}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" uninstall --installPath \"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\"", "InstallLocation": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community", "Version": "16.9.25", "Name": "Visual Studio Community 2019", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\Installer\\setup.exe\" uninstall --installPath \"C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\"", "InstallLocation": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community", "Version": "17.3.3", "Name": "Visual Studio Community 2022", "EstimatedSizeMB": "N/A"}, {"InstallDate": "20250318", "Publisher": "ZMorph", "UninstallString": "MsiExec.exe /X{65F9B006-91AD-47D2-88B6-AE2BDF8C1810}", "InstallLocation": "N/A", "Version": "2.0.0.1155", "Name": "Voxelizer 3", "EstimatedSizeMB": 121.58}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{A4272808-82F5-410F-A5F9-1BF6F63F6B9A}", "InstallLocation": "N/A", "Version": "**********", "Name": "VS Script Debugging Common", "EstimatedSizeMB": 4.48}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{757CECF6-0860-410C-B4C6-5022CB4785D8}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_clickoncebootstrappermsi", "EstimatedSizeMB": 0.52}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{2E553B80-869B-48D8-A8DE-99211DA296B1}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_clickoncebootstrappermsires", "EstimatedSizeMB": 0.14}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{1A590AE3-23D1-4C79-9631-23120BCC12AE}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_clickoncesigntoolmsi", "EstimatedSizeMB": 0.3}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{375AFBC1-2264-470C-9ADE-2C0BF23328A2}", "InstallLocation": "N/A", "Version": "16.11.34930", "Name": "vs_communitymsi", "EstimatedSizeMB": 36.66}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{48B51989-95EE-4168-9911-127E9A4CDF14}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_communitymsires", "EstimatedSizeMB": 0.07}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{71498EE6-F94E-4061-9DD5-55925CA8A74F}", "InstallLocation": "N/A", "Version": "17.14.36025", "Name": "vs_communitysharedmsi", "EstimatedSizeMB": 32.29}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{3873679C-FA03-4101-97E9-107D67C568B8}", "InstallLocation": "N/A", "Version": "17.14.36025", "Name": "vs_communityx64msi", "EstimatedSizeMB": 1.09}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{D574B266-B723-4BF0-B701-C35212C6EE0F}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_CoreEditorFonts", "EstimatedSizeMB": 0.71}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{AD0C92A4-1514-4BC1-A723-A272A8343924}", "InstallLocation": "N/A", "Version": "16.0.28329", "Name": "vs_devenvmsi", "EstimatedSizeMB": 0.12}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{4A0A909C-3FA3-4EF8-999D-C1EC8D662833}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_devenvsharedmsi", "EstimatedSizeMB": 0.18}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{5B4AAF86-AA42-4C32-BE98-A057A6F118BD}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_devenx64vmsi", "EstimatedSizeMB": 0.18}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{22EE1419-B47E-44B8-A635-868746946079}", "InstallLocation": "N/A", "Version": "17.14.36024", "Name": "vs_filehandler_amd64", "EstimatedSizeMB": 1.95}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{6C7D0172-7367-4F0C-95DB-6021765B58EA}", "InstallLocation": "N/A", "Version": "17.14.36024", "Name": "vs_filehandler_x86", "EstimatedSizeMB": 1.9}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{D331F4E7-BC3B-47BE-BB56-5F33895F9347}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_FileTracker_Singleton", "EstimatedSizeMB": 1.45}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{29297AFE-9D24-4DFE-ACAF-D90090D905CF}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_githubprotocolhandlermsi", "EstimatedSizeMB": 3.94}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{883D29E5-9A41-4C45-A192-C10B8078BF0C}", "InstallLocation": "N/A", "Version": "16.10.31306", "Name": "vs_minshe<PERSON><PERSON><PERSON><PERSON>", "EstimatedSizeMB": 1.65}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{537F07C8-0EC6-41A5-BB91-750FA3CAA110}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_minshellinteropsharedmsi", "EstimatedSizeMB": 1.57}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{B797608C-8536-46EF-95D8-8474139EA43A}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_minshellinteropx64msi", "EstimatedSizeMB": 0.07}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{C1337DAC-D78B-4435-B795-29E8B7D5E75C}", "InstallLocation": "N/A", "Version": "16.11.34902", "Name": "vs_minshellmsi", "EstimatedSizeMB": 0.13}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{8D405766-FAE1-4709-AB22-10820135311F}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_minshellmsires", "EstimatedSizeMB": 0.14}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{F6E69DD8-7652-4CC6-8A4F-485A16012B5B}", "InstallLocation": "N/A", "Version": "17.14.36024", "Name": "vs_minshellsharedmsi", "EstimatedSizeMB": 0.27}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{8A87F636-C465-43ED-B10D-00DA3F4DC99B}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_minshellx64msi", "EstimatedSizeMB": 0.0}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{B7A0C185-CD7C-4405-A06D-617728AFC598}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_SQLClickOnceBootstrappermsi", "EstimatedSizeMB": 12.17}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{73328FDB-39A8-4A80-B933-805F1B233F94}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_ssmsprotocolselectormsi", "EstimatedSizeMB": 1.38}, {"InstallDate": "20250627", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{3522E976-69DC-4146-81E4-286787101385}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_ssmsprotocolselectormsires", "EstimatedSizeMB": 0.43}, {"InstallDate": "20220912", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{CE7C416B-33D1-446A-8349-6AAB3C2331B1}", "InstallLocation": "N/A", "Version": "17.3.32708", "Name": "vs_tipsmsi", "EstimatedSizeMB": 0.04}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{9561B8F2-C389-4FB0-BBED-0BA38B69BAA8}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_vswebprotocolselectormsi", "EstimatedSizeMB": 1.34}, {"InstallDate": "20250626", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{42A57A99-7365-4524-A0CC-AFFB4EB085B0}", "InstallLocation": "N/A", "Version": "17.14.36015", "Name": "vs_vswebprotocolselectormsires", "EstimatedSizeMB": 0.41}, {"InstallDate": "20220919", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{513D2E7B-CDC1-36F1-AD64-7274C2B89D19}", "InstallLocation": "N/A", "Version": "16.0.31004", "Name": "Windows Phone SDK 8.0 Assemblies for Visual Studio 2019", "EstimatedSizeMB": 2.44}, {"InstallDate": "20250129", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /I{E18618EC-D9DB-4BCE-B382-85ADA2CBB340}", "InstallLocation": "N/A", "Version": "10.1.0.0", "Name": "Windows SDK AddOn", "EstimatedSizeMB": 0.15}, {"InstallDate": "20250516", "Publisher": "Microsoft Corporation", "UninstallString": "MsiExec.exe /X{1D48774E-EC31-48BA-ABEE-EF92019BAC42}", "InstallLocation": "N/A", "Version": "2.4.13.0", "Name": "Windows Subsystem for Linux", "EstimatedSizeMB": 644.53}, {"InstallDate": "N/A", "Publisher": "win.rar GmbH", "UninstallString": "C:\\Program Files\\WinRAR\\uninstall.exe", "InstallLocation": "C:\\Program Files\\WinRAR\\", "Version": "6.11.0", "Name": "WinRAR 6.11 (64-bit)", "EstimatedSizeMB": "N/A"}, {"InstallDate": "N/A", "Publisher": "The Wireshark developer community, https://www.wireshark.org", "UninstallString": "\"C:\\Program Files\\Wireshark\\uninstall-wireshark.exe\"", "InstallLocation": "C:\\Program Files\\Wireshark", "Version": "4.4.5", "Name": "Wireshark 4.4.5 x64", "EstimatedSizeMB": 239.5}, {"InstallDate": "20241016", "Publisher": "Apache Friends", "UninstallString": "\"C:\\xampp2\\uninstall.exe\"", "InstallLocation": "C:\\xampp2", "Version": "8.2.12-0", "Name": "XAMPP", "EstimatedSizeMB": 704.57}, {"InstallDate": "20220912", "Publisher": "xSQL Software", "UninstallString": "MsiExec.exe /X{FF7CF9B8-D015-4CAA-BB5C-1008E8492461}", "InstallLocation": "N/A", "Version": "11.1.0", "Name": "xSQL Comparison Bundle for SQL Server v11", "EstimatedSizeMB": 36.41}, {"InstallDate": "N/A", "Publisher": "Zebra Technologies", "UninstallString": "\"C:\\ProgramData\\{46EC6AB8-42F9-4907-A70E-848A755D737F}\\zsu-1-1-9-1325.exe\" REMOVE=TRUE MODIFY=FALSE", "InstallLocation": "C:\\Program Files (x86)\\Zebra Technologies\\Zebra Setup Utilities", "Version": "1.1.9.1325", "Name": "Zebra Setup Utilities", "EstimatedSizeMB": "N/A"}], "CollectionDate": "2025-07-31 12:57:36"}