# Script completo para coleta de informações do sistema
# Coleta: Licenças Windows, Office (método otimizado), Hardware, Software instalado
# Saída: JSON formatado com nome do computador

# Função para obter informações de licença do Windows
function Get-WindowsLicenseInfo {
    try {
        $licenseInfo = @{}
        
        # Informações básicas do Windows
        $os = Get-CimInstance -ClassName Win32_OperatingSystem
        $licenseInfo.WindowsVersion = $os.Caption
        $licenseInfo.WindowsBuild = $os.BuildNumber
        
        # Múltiplos métodos para verificar ativação
        # Método 1: slmgr /xpr
        try {
            $slmgrXpr = & cscript //nologo "$env:windir\system32\slmgr.vbs" /xpr 2>$null
            $licenseInfo.ActivationStatus = if ($slmgrXpr -match "permanently activated|ativado permanentemente") { "Activated" } else { "Not Activated" }
        } catch {
            $errorMsg = $_.Exception.Message
            Write-Warning "Erro ao executar slmgr /xpr: $errorMsg"
        }
        
        # Método 2: slmgr /dli (informações detalhadas)
        try {
            $slmgrDli = & cscript //nologo "$env:windir\system32\slmgr.vbs" /dli 2>$null
            $dliText = $slmgrDli -join "`n"
            
            # Extrair informações do /dli
            if ($dliText -match "License Status:\s*(.+)") {
                $licenseInfo.LICENSE_STATUS = $matches[1].Trim()
                if ($matches[1].Trim() -match "Licensed|Licenciado") {
                    $licenseInfo.ActivationStatus = "Activated"
                }
            }
            
            if ($dliText -match "Partial Product Key:\s*(.+)") {
                $licenseInfo.PARTIAL_KEY = $matches[1].Trim()
            }
            
            if ($dliText -match "Description:\s*(.+)") {
                $licenseInfo.PRODUCT_DESCRIPTION = $matches[1].Trim()
            }
        } catch {
            Write-Warning "Erro ao executar slmgr /dli: $($_.Exception.Message)"
        }
        
        # Método 3: slmgr /dlv (informações verbosas)
        try {
            $slmgrDlv = & cscript //nologo "$env:windir\system32\slmgr.vbs" /dlv 2>$null
            $dlvText = $slmgrDlv -join "`n"
            
            if ($dlvText -match "Product Key Channel:\s*(.+)") {
                $licenseInfo.LICENSE_CHANNEL = $matches[1].Trim()
            }
        } catch {
            Write-Warning "Erro ao executar slmgr /dlv: $($_.Exception.Message)"
        }
        
        # Método 4: CIM SoftwareLicensingProduct
        try {
            $licensingProducts = Get-CimInstance -Query "SELECT * FROM SoftwareLicensingProduct WHERE ApplicationID='55c92734-d682-4d71-983e-d6ec3f16059f' AND LicenseStatus=1"
            if ($licensingProducts) {
                $mainProduct = $licensingProducts | Where-Object { $_.PartialProductKey } | Select-Object -First 1
                if ($mainProduct) {
                    $licenseInfo.LICENSE_TYPE = $mainProduct.ProductKeyChannel
                    $licenseInfo.LicenseFamily = $mainProduct.LicenseFamily
                    $licenseInfo.PARTIAL_KEY = $mainProduct.PartialProductKey
                    $licenseInfo.ActivationStatus = "Activated"
                }
            }
        } catch {
            Write-Warning "Erro ao consultar SoftwareLicensingProduct: $($_.Exception.Message)"
        }
        
        # Método 5: Registro do Windows para chave OEM/OA3
        try {
            $oa3Key = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SoftwareProtectionPlatform" -Name "BackupProductKeyDefault" -ErrorAction SilentlyContinue
            if ($oa3Key -and $oa3Key.BackupProductKeyDefault) {
                $licenseInfo.OA3X_KEY = $oa3Key.BackupProductKeyDefault
            }
        } catch {
            Write-Warning "Erro ao acessar chave OA3: $($_.Exception.Message)"
        }
        
        # Verificar se há chave do produto completa (para casos específicos)
        try {
            $fullKey = (Get-CimInstance -ClassName "SoftwareLicensingService").OA3xOriginalProductKey
            if ($fullKey) {
                $licenseInfo.PRODUCT_KEY = $fullKey
            }
        } catch {
            Write-Warning "Erro ao obter chave completa do produto: $($_.Exception.Message)"
        }
        
        return $licenseInfo
    }
    catch {
        Write-Error "Erro ao obter informações de licença do Windows: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter informações de licença do Windows: $($_.Exception.Message)" }
    }
}

# Função otimizada para obter informações de licença do Office
function Get-OfficeLicenseInfo {
    try {
        $officeInfo = @{
            ActiveLicenses = @()
            Status = "Not Found"
            DetectionMethod = "Registry Analysis"
        }
        
        Write-Host "🔍 Verificando licenças ativas do Office..." -ForegroundColor Yellow
        
        # Método principal: Verificar mapeamento de licenças ativas (Office 16.0+)
        $emailMappingPath = "HKCU:\Software\Microsoft\Office\16.0\Common\Licensing\LicensingNext\LicenseIdToEmailMapping"
        $cidMappingPath = "HKCU:\Software\Microsoft\Office\16.0\Common\Licensing\LicensingNext\CIDToLicenseIdsMapping"
        
        if ((Test-Path $emailMappingPath) -and (Test-Path $cidMappingPath)) {
            # Obter mapeamento LicenseId -> Email
            $emailMapping = @{}
            $emailProps = Get-ItemProperty -Path $emailMappingPath -ErrorAction SilentlyContinue
            
            if ($emailProps) {
                $emailProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                    $licenseId = $_.Name
                    $emailData = $_.Value
                    
                    # Parse do JSON se necessário
                    try {
                        if ($emailData -like "*{*}*") {
                            $jsonData = $emailData | ConvertFrom-Json
                            $email = $jsonData.PSObject.Properties.Value | Select-Object -First 1
                        } else {
                            $email = $emailData
                        }
                        $emailMapping[$licenseId] = $email
                    } catch {
                        $emailMapping[$licenseId] = $emailData
                    }
                }
            }
            
            # Obter CID ativo e correlacionar com emails
            $cidProps = Get-ItemProperty -Path $cidMappingPath -ErrorAction SilentlyContinue
            
            if ($cidProps) {
                $cidProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                    $cid = $_.Name
                    $licenseData = $_.Value
                    
                    # Verificar se este CID corresponde a algum License ID
                    if ($emailMapping.ContainsKey($cid)) {
                        $activeEmail = $emailMapping[$cid]
                        $officeInfo.ActiveLicenses += @{
                            EmailAddress = $activeEmail
                            LicenseID = $cid
                            CID = $cid
                            Status = "Active"
                            Method = "LicensingNext"
                            OfficeVersion = "16.0"
                        }
                        $officeInfo.Status = "Active"
                    }
                }
            }
        }
        
        # Método alternativo: Verificar versões mais antigas do Office (15.0, 14.0)
        if ($officeInfo.ActiveLicenses.Count -eq 0) {
            $olderVersions = @("15.0", "14.0")
            
            foreach ($version in $olderVersions) {
                $oldEmailPath = "HKCU:\Software\Microsoft\Office\$version\Common\Licensing\LicensingNext\LicenseIdToEmailMapping"
                $oldCidPath = "HKCU:\Software\Microsoft\Office\$version\Common\Licensing\LicensingNext\CIDToLicenseIdsMapping"
                
                if ((Test-Path $oldEmailPath) -and (Test-Path $oldCidPath)) {
                    # Mesmo processo para versões antigas
                    $oldEmailMapping = @{}
                    $oldEmailProps = Get-ItemProperty -Path $oldEmailPath -ErrorAction SilentlyContinue
                    
                    if ($oldEmailProps) {
                        $oldEmailProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                            $licenseId = $_.Name
                            $emailData = $_.Value
                            
                            try {
                                if ($emailData -like "*{*}*") {
                                    $jsonData = $emailData | ConvertFrom-Json
                                    $email = $jsonData.PSObject.Properties.Value | Select-Object -First 1
                                } else {
                                    $email = $emailData
                                }
                                $oldEmailMapping[$licenseId] = $email
                            } catch {
                                $oldEmailMapping[$licenseId] = $emailData
                            }
                        }
                    }
                    
                    $oldCidProps = Get-ItemProperty -Path $oldCidPath -ErrorAction SilentlyContinue
                    
                    if ($oldCidProps) {
                        $oldCidProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                            $cid = $_.Name
                            
                            if ($oldEmailMapping.ContainsKey($cid)) {
                                $activeEmail = $oldEmailMapping[$cid]
                                $officeInfo.ActiveLicenses += @{
                                    EmailAddress = $activeEmail
                                    LicenseID = $cid
                                    CID = $cid
                                    Status = "Active"
                                    Method = "LegacyLicensing"
                                    OfficeVersion = $version
                                }
                                $officeInfo.Status = "Active"
                            }
                        }
                    }
                }
            }
        }
        
        # Informações complementares do Office
        try {
            # Verificar versão instalada via Click-to-Run
            $c2rPath = "HKLM:\SOFTWARE\Microsoft\Office\ClickToRun\Configuration"
            if (Test-Path $c2rPath) {
                $c2rInfo = Get-ItemProperty -Path $c2rPath -ErrorAction SilentlyContinue
                if ($c2rInfo) {
                    $officeInfo.ClickToRunInfo = @{
                        Platform = $c2rInfo.Platform
                        Channel = $c2rInfo.CDNBaseUrl
                        Version = $c2rInfo.VersionToReport
                        ProductIds = $c2rInfo.ProductReleaseIds
                        UpdateChannel = $c2rInfo.UpdateChannel
                        ClientCulture = $c2rInfo.ClientCulture
                        InstallType = "Click-to-Run"
                    }
                }
            }
            
            # Verificar processos do Office em execução
            $officeProcesses = Get-Process | Where-Object { $_.ProcessName -match "OUTLOOK|WINWORD|EXCEL|POWERPNT|ONENOTE|MSACCESS|TEAMS" } -ErrorAction SilentlyContinue
            if ($officeProcesses) {
                $officeInfo.RunningProcesses = $officeProcesses | ForEach-Object { 
                    @{
                        ProcessName = $_.ProcessName
                        Id = $_.Id
                        StartTime = if ($_.StartTime) { $_.StartTime.ToString("yyyy-MM-dd HH:mm:ss") } else { "N/A" }
                        WindowTitle = try { $_.MainWindowTitle } catch { "N/A" }
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao obter informações complementares do Office: $($_.Exception.Message)"
        }
        
        return $officeInfo
    }
    catch {
        Write-Error "Erro ao obter informações do Office: $($_.Exception.Message)"
        return @{ 
            Error = "Erro ao obter informações do Office: $($_.Exception.Message)"
            Status = "Error"
            ActiveLicenses = @()
        }
    }
}

# Função para obter informações do sistema
function Get-SystemInfo {
    try {
        $systemInfo = @{}
        
        # Informações do computador
        $computerSystem = Get-CimInstance -ClassName Win32_ComputerSystem
        $systemInfo.ComputerName = $computerSystem.Name
        $systemInfo.Manufacturer = $computerSystem.Manufacturer
        $systemInfo.Model = $computerSystem.Model
        $systemInfo.TotalPhysicalMemoryGB = [math]::Round($computerSystem.TotalPhysicalMemory / 1GB, 2)
        
        # Informações do processador
        $processor = Get-CimInstance -ClassName Win32_Processor | Select-Object -First 1
        $systemInfo.Processor = @{
            Name = $processor.Name
            Cores = $processor.NumberOfCores
            LogicalProcessors = $processor.NumberOfLogicalProcessors
            MaxClockSpeed = $processor.MaxClockSpeed
            Architecture = $processor.Architecture
        }
        
        # Informações da placa-mãe
        $motherboard = Get-CimInstance -ClassName Win32_BaseBoard
        $systemInfo.Motherboard = @{
            Manufacturer = $motherboard.Manufacturer
            Product = $motherboard.Product
            SerialNumber = $motherboard.SerialNumber
            Version = $motherboard.Version
        }
        
        # Informações de memória RAM
        $memory = Get-CimInstance -ClassName Win32_PhysicalMemory
        $systemInfo.Memory = @()
        foreach ($mem in $memory) {
            $systemInfo.Memory += @{
                CapacityGB = [math]::Round($mem.Capacity / 1GB, 2)
                Speed = $mem.Speed
                Manufacturer = $mem.Manufacturer
                PartNumber = $mem.PartNumber
                SerialNumber = $mem.SerialNumber
            }
        }
        
        # Informações de armazenamento
        $drives = Get-CimInstance -ClassName Win32_DiskDrive
        $systemInfo.Storage = @()
        foreach ($drive in $drives) {
            $systemInfo.Storage += @{
                Model = $drive.Model
                SizeGB = [math]::Round($drive.Size / 1GB, 2)
                InterfaceType = $drive.InterfaceType
                SerialNumber = $drive.SerialNumber
                MediaType = $drive.MediaType
            }
        }
        
        # Informações da placa de vídeo
        $graphics = Get-CimInstance -ClassName Win32_VideoController
        $systemInfo.Graphics = @()
        foreach ($gpu in $graphics) {
            if ($gpu.Name -notlike "*Basic*" -and $gpu.Name -notlike "*VGA*") {
                $systemInfo.Graphics += @{
                    Name = $gpu.Name
                    AdapterRAMMB = if ($gpu.AdapterRAM) { [math]::Round($gpu.AdapterRAM / 1MB, 2) } else { "N/A" }
                    DriverVersion = $gpu.DriverVersion
                    DriverDate = $gpu.DriverDate
                }
            }
        }
        
        # Informações de rede
        $networkAdapters = Get-CimInstance -ClassName Win32_NetworkAdapter | Where-Object { $_.NetConnectionStatus -eq 2 }
        $systemInfo.Network = @()
        foreach ($adapter in $networkAdapters) {
            $systemInfo.Network += @{
                Name = $adapter.Name
                Manufacturer = $adapter.Manufacturer
                MACAddress = $adapter.MACAddress
                Speed = $adapter.Speed
            }
        }
        
        # Informações do BIOS
        $bios = Get-CimInstance -ClassName Win32_BIOS
        $systemInfo.BIOS = @{
            Manufacturer = $bios.Manufacturer
            Version = $bios.Version
            ReleaseDate = $bios.ReleaseDate
            SerialNumber = $bios.SerialNumber
        }
        
        return $systemInfo
    }
    catch {
        Write-Error "Erro ao obter informações do sistema: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter informações do sistema: $($_.Exception.Message)" }
    }
}

# Função para obter aplicativos instalados
function Get-InstalledApplications {
    try {
        $applications = @()
        
        # Registro do Windows para aplicativos instalados
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        foreach ($path in $registryPaths) {
            try {
                $apps = Get-ItemProperty -Path $path -ErrorAction SilentlyContinue
                foreach ($app in $apps) {
                    if ($app.DisplayName -and $app.DisplayName.Trim() -ne "") {
                        $applications += @{
                            Name = $app.DisplayName
                            Version = if ($app.DisplayVersion) { $app.DisplayVersion } else { "N/A" }
                            Publisher = if ($app.Publisher) { $app.Publisher } else { "N/A" }
                            InstallDate = if ($app.InstallDate) { $app.InstallDate } else { "N/A" }
                            InstallLocation = if ($app.InstallLocation) { $app.InstallLocation } else { "N/A" }
                            UninstallString = if ($app.UninstallString) { $app.UninstallString } else { "N/A" }
                            EstimatedSizeMB = if ($app.EstimatedSize) { [math]::Round($app.EstimatedSize / 1024, 2) } else { "N/A" }
                        }
                    }
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Warning "Erro ao processar registry path ${path}: $errorMsg"
            }
        }
        
        # Remover duplicatas baseado no nome
        $uniqueApplications = $applications | Sort-Object Name | Group-Object Name | ForEach-Object { $_.Group | Select-Object -First 1 }
        
        return $uniqueApplications
    }
    catch {
        Write-Error "Erro ao obter aplicativos instalados: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter aplicativos instalados: $($_.Exception.Message)" }
    }
}

# Função principal
function Get-CompleteSystemInfo {
    Write-Host "Coletando informações do sistema..." -ForegroundColor Green
    
    $systemData = @{
        CollectionDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        ComputerName = $env:COMPUTERNAME
        WindowsLicense = Get-WindowsLicenseInfo
        OfficeLicense = Get-OfficeLicenseInfo
        SystemInfo = Get-SystemInfo
        InstalledApplications = Get-InstalledApplications
    }
    
    return $systemData
}

# Executar coleta e converter para JSON
Write-Host "=== INICIANDO COLETA DE INFORMAÇÕES DO SISTEMA ===" -ForegroundColor Cyan
Write-Host ""

try {
    $completeInfo = Get-CompleteSystemInfo

    Write-Host ""
    Write-Host "=== CONVERTENDO PARA JSON ===" -ForegroundColor Cyan

    # Converter para JSON com formatação
    $jsonOutput = $completeInfo | ConvertTo-Json -Depth 10

    # Exibir resultado
    Write-Host ""
    Write-Host "=== RESULTADO EM JSON ===" -ForegroundColor Green
    Write-Host $jsonOutput

    # Salvar em arquivo com nome do computador
    $computerName = $env:COMPUTERNAME
    $outputFile = "$computerName.json"
    $jsonOutput | Out-File -FilePath $outputFile -Encoding UTF8

    Write-Host ""
    Write-Host "Informações salvas em: $outputFile" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "=== COLETA FINALIZADA ===" -ForegroundColor Cyan
}
catch {
    Write-Error "Erro durante a execução do script: $($_.Exception.Message)"
    Write-Host "Stack Trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}