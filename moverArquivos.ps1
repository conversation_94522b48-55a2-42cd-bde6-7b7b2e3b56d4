# Script para mover arquivos de licenças para a rede
# Versão separada - Apenas movimenta arquivos existentes
# Não requer permissões de administrador

# Configurações
$LocalPath = "C:\wallpaper"
$NetworkPath = "\\*************\onix\TESTE-TI\TESTE1234\12345\w_lic_ti\"
$ComputerName = $env:COMPUTERNAME
$LocalFile = "$LocalPath\$ComputerName.txt"
$NetworkFile = "$NetworkPath\$ComputerName.txt"

# Função para verificar se o arquivo local existe
function Test-LocalFile {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        Write-Host "Arquivo local encontrado: $FilePath" -ForegroundColor Green
        return $true
    }
    else {
        Write-Host "Arquivo local não encontrado: $FilePath" -ForegroundColor Red
        Write-Host "Execute primeiro o script de geração de licenças." -ForegroundColor Yellow
        return $false
    }
}

# Função para testar conectividade com a rede
function Test-NetworkConnectivity {
    param([string]$NetworkPath)
    
    try {
        # Testa se o caminho da rede está acessível
        if (Test-Path $NetworkPath) {
            Write-Host "Conectividade com a rede OK: $NetworkPath" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Caminho da rede não acessível: $NetworkPath" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Erro ao testar conectividade: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Função para criar diretório de rede se necessário
function Ensure-NetworkDirectory {
    param([string]$Path)
    
    try {
        if (-not (Test-Path $Path)) {
            Write-Host "Criando diretório de rede: $Path" -ForegroundColor Yellow
            New-Item -ItemType Directory -Path $Path -Force | Out-Null
            Write-Host "Diretório criado com sucesso." -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Diretório de rede já existe: $Path" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "Erro ao criar diretório de rede: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Função para mover arquivo para rede
function Move-FileToNetwork {
    param([string]$LocalFile, [string]$NetworkFile)
    
    try {
        # Copiar arquivo para rede
        Write-Host "Copiando arquivo para rede..." -ForegroundColor Yellow
        Copy-Item -Path $LocalFile -Destination $NetworkFile -Force
        
        # Verificar se a cópia foi bem-sucedida
        if (Test-Path $NetworkFile) {
            Write-Host "Arquivo copiado com sucesso: $NetworkFile" -ForegroundColor Green
            
            # Remover arquivo local após cópia bem-sucedida
            Write-Host "Removendo arquivo local..." -ForegroundColor Yellow
            Remove-Item -Path $LocalFile -Force
            Write-Host "Arquivo local removido com sucesso." -ForegroundColor Green
            
            return $true
        }
        else {
            Write-Host "Erro: Arquivo não foi copiado corretamente." -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Erro ao mover arquivo: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Função para obter informações do arquivo
function Get-FileInfo {
    param([string]$FilePath)
    
    try {
        if (Test-Path $FilePath) {
            $file = Get-Item $FilePath
            return @{
                Name = $file.Name
                Size = $file.Length
                CreationTime = $file.CreationTime
                LastWriteTime = $file.LastWriteTime
            }
        }
        return $null
    }
    catch {
        return $null
    }
}

# Função para exibir resumo da operação
function Show-OperationSummary {
    param([bool]$Success, [string]$LocalFile, [string]$NetworkFile)
    
    Write-Host ""
    Write-Host "=== RESUMO DA OPERAÇÃO ===" -ForegroundColor Cyan
    Write-Host "Computador: $ComputerName" -ForegroundColor White
    Write-Host "Data/Hora: $(Get-Date)" -ForegroundColor White
    Write-Host "Arquivo origem: $LocalFile" -ForegroundColor White
    Write-Host "Arquivo destino: $NetworkFile" -ForegroundColor White
    
    if ($Success) {
        Write-Host "Status: SUCESSO" -ForegroundColor Green
        Write-Host "Arquivo movido com sucesso para a rede." -ForegroundColor Green
        
        # Verificar se arquivo de rede existe e mostrar informações
        $networkInfo = Get-FileInfo -FilePath $NetworkFile
        if ($networkInfo) {
            Write-Host "Tamanho: $($networkInfo.Size) bytes" -ForegroundColor White
            Write-Host "Última modificação: $($networkInfo.LastWriteTime)" -ForegroundColor White
        }
    }
    else {
        Write-Host "Status: FALHA" -ForegroundColor Red
        Write-Host "Não foi possível mover o arquivo para a rede." -ForegroundColor Red
        
        # Verificar se arquivo local ainda existe
        if (Test-Path $LocalFile) {
            Write-Host "Arquivo permanece disponível localmente: $LocalFile" -ForegroundColor Yellow
        }
    }
}

# Função principal
function Main {
    Write-Host "=== MOVIMENTADOR DE ARQUIVOS DE LICENÇAS ===" -ForegroundColor Cyan
    Write-Host "Computador: $ComputerName" -ForegroundColor White
    Write-Host "Data/Hora: $(Get-Date)" -ForegroundColor White
    Write-Host ""
    
    # Verificar se arquivo local existe
    if (-not (Test-LocalFile -FilePath $LocalFile)) {
        Write-Host "Operação cancelada." -ForegroundColor Red
        exit 1
    }
    
    # Mostrar informações do arquivo local
    $localInfo = Get-FileInfo -FilePath $LocalFile
    if ($localInfo) {
        Write-Host "Informações do arquivo local:" -ForegroundColor White
        Write-Host "  Nome: $($localInfo.Name)" -ForegroundColor Gray
        Write-Host "  Tamanho: $($localInfo.Size) bytes" -ForegroundColor Gray
        Write-Host "  Criado em: $($localInfo.CreationTime)" -ForegroundColor Gray
        Write-Host "  Modificado em: $($localInfo.LastWriteTime)" -ForegroundColor Gray
        Write-Host ""
    }
    
    # Testar conectividade com a rede
    Write-Host "Testando conectividade com a rede..." -ForegroundColor Yellow
    if (-not (Test-NetworkConnectivity -NetworkPath $NetworkPath)) {
        Write-Host "Não foi possível conectar com a rede. Verifique:" -ForegroundColor Red
        Write-Host "  - Conectividade de rede" -ForegroundColor Yellow
        Write-Host "  - Permissões de acesso ao compartilhamento" -ForegroundColor Yellow
        Write-Host "  - Caminho da rede: $NetworkPath" -ForegroundColor Yellow
        Write-Host ""
        Show-OperationSummary -Success $false -LocalFile $LocalFile -NetworkFile $NetworkFile
        exit 1
    }
    
    # Garantir que o diretório de rede existe
    Write-Host "Verificando diretório de rede..." -ForegroundColor Yellow
    if (-not (Ensure-NetworkDirectory -Path $NetworkPath)) {
        Write-Host "Não foi possível criar/acessar diretório de rede." -ForegroundColor Red
        Show-OperationSummary -Success $false -LocalFile $LocalFile -NetworkFile $NetworkFile
        exit 1
    }
    
    # Mover arquivo para rede
    Write-Host "Iniciando movimentação do arquivo..." -ForegroundColor Yellow
    $success = Move-FileToNetwork -LocalFile $LocalFile -NetworkFile $NetworkFile
    
    # Exibir resumo da operação
    Show-OperationSummary -Success $success -LocalFile $LocalFile -NetworkFile $NetworkFile
    
    if ($success) {
        Write-Host ""
        Write-Host "Movimentação concluída com sucesso!" -ForegroundColor Green
        exit 0
    }
    else {
        Write-Host ""
        Write-Host "Movimentação falhou. Verifique os logs acima." -ForegroundColor Red
        exit 1
    }
}

# Executar script principal
try {
    Main
}
catch {
    Write-Host "Erro geral na execução: $($_.Exception.Message)" -ForegroundColor Red
    Show-OperationSummary -Success $false -LocalFile $LocalFile -NetworkFile $NetworkFile
    exit 1
}