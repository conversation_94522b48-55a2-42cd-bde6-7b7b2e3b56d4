﻿# Script completo para coleta de informações do sistema
# Coleta: Licenças Windows, Office, Hardware, Software instalado
# Saída: JSON formatado com nome do computador

# Função para obter informações de licença do Windows
function Get-WindowsLicenseInfo {
    try {
        $licenseInfo = @{}
        
        # Informações básicas do Windows
        $os = Get-CimInstance -ClassName Win32_OperatingSystem
        $licenseInfo.WindowsVersion = $os.Caption
        $licenseInfo.WindowsBuild = $os.BuildNumber
        
        # Múltiplos métodos para verificar ativação
        # Método 1: slmgr /xpr
        try {
            $slmgrXpr = & cscript //nologo "$env:windir\system32\slmgr.vbs" /xpr 2>$null
            $licenseInfo.ActivationStatus = if ($slmgrXpr -match "permanently activated|ativado permanentemente") { "Activated" } else { "Not Activated" }
        } catch {
            $errorMsg = $_.Exception.Message
            Write-Warning "Erro ao executar slmgr /xpr: $errorMsg"
        }
        
        # Método 2: slmgr /dli (informações detalhadas)
        try {
            $slmgrDli = & cscript //nologo "$env:windir\system32\slmgr.vbs" /dli 2>$null
            $dliText = $slmgrDli -join "`n"
            
            # Extrair informações do /dli
            if ($dliText -match "License Status:\s*(.+)") {
                $licenseInfo.LICENSE_STATUS = $matches[1].Trim()
                if ($matches[1].Trim() -match "Licensed|Licenciado") {
                    $licenseInfo.ActivationStatus = "Activated"
                }
            }
            
            if ($dliText -match "Partial Product Key:\s*(.+)") {
                $licenseInfo.PARTIAL_KEY = $matches[1].Trim()
            }
            
            if ($dliText -match "Description:\s*(.+)") {
                $licenseInfo.PRODUCT_DESCRIPTION = $matches[1].Trim()
            }
        } catch {
            Write-Warning "Erro ao executar slmgr /dli: $($_.Exception.Message)"
        }
        
        # Método 3: slmgr /dlv (informações verbosas)
        try {
            $slmgrDlv = & cscript //nologo "$env:windir\system32\slmgr.vbs" /dlv 2>$null
            $dlvText = $slmgrDlv -join "`n"
            
            if ($dlvText -match "Product Key Channel:\s*(.+)") {
                $licenseInfo.LICENSE_CHANNEL = $matches[1].Trim()
            }
        } catch {
            Write-Warning "Erro ao executar slmgr /dlv: $($_.Exception.Message)"
        }
        
        # Método 4: CIM SoftwareLicensingProduct
        try {
            $licensingProducts = Get-CimInstance -Query "SELECT * FROM SoftwareLicensingProduct WHERE ApplicationID='55c92734-d682-4d71-983e-d6ec3f16059f' AND LicenseStatus=1"
            if ($licensingProducts) {
                $mainProduct = $licensingProducts | Where-Object { $_.PartialProductKey } | Select-Object -First 1
                if ($mainProduct) {
                    $licenseInfo.LICENSE_TYPE = $mainProduct.ProductKeyChannel
                    $licenseInfo.LicenseFamily = $mainProduct.LicenseFamily
                    $licenseInfo.PARTIAL_KEY = $mainProduct.PartialProductKey
                    $licenseInfo.ActivationStatus = "Activated"
                }
            }
        } catch {
            Write-Warning "Erro ao consultar SoftwareLicensingProduct: $($_.Exception.Message)"
        }
        
        # Método 5: Registro do Windows para chave OEM/OA3
        try {
            $oa3Key = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\SoftwareProtectionPlatform" -Name "BackupProductKeyDefault" -ErrorAction SilentlyContinue
            if ($oa3Key -and $oa3Key.BackupProductKeyDefault) {
                $licenseInfo.OA3X_KEY = $oa3Key.BackupProductKeyDefault
            }
        } catch {
            Write-Warning "Erro ao acessar chave OA3: $($_.Exception.Message)"
        }
        
        # Verificar se há chave do produto completa (para casos específicos)
        try {
            $fullKey = (Get-CimInstance -ClassName "SoftwareLicensingService").OA3xOriginalProductKey
            if ($fullKey) {
                $licenseInfo.PRODUCT_KEY = $fullKey
            }
        } catch {
            Write-Warning "Erro ao obter chave completa do produto: $($_.Exception.Message)"
        }
        
        return $licenseInfo
    }
    catch {
        Write-Error "Erro ao obter informações de licença do Windows: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter informações de licença do Windows: $($_.Exception.Message)" }
    }
}

# Função para obter informações de licença do Office
function Get-OfficeLicenseInfo {
    try {
        $officeInfo = @{}
        $allEmails = @()
        
        # Método 1: Verificar conta logada do Office 365 via registro
        try {
            $officeInfo.Office365Accounts = @()
            
            # Verificar múltiplas versões do Office
            $officeVersions = @("16.0", "15.0", "14.0", "12.0")
            
            foreach ($version in $officeVersions) {
                # Verificar identidades no HKCU
                $identityPaths = @(
                    "HKCU:\SOFTWARE\Microsoft\Office\$version\Common\Identity\Identities",
                    "HKCU:\SOFTWARE\Microsoft\Office\$version\Common\Identity\Settings",
                    "HKCU:\SOFTWARE\Microsoft\Office\$version\Common\Roaming\Identities",
                    "HKCU:\SOFTWARE\Microsoft\Office\$version\Outlook\Profiles\Outlook"
                )
                
                foreach ($identityPath in $identityPaths) {
                    if (Test-Path $identityPath) {
                        try {
                            $identityKeys = Get-ChildItem -Path $identityPath -Recurse -ErrorAction SilentlyContinue
                            foreach ($key in $identityKeys) {
                                $identity = Get-ItemProperty -Path $key.PSPath -ErrorAction SilentlyContinue
                                
                                # Verificar múltiplas propriedades que podem conter emails
                                $emailProperties = @("EmailAddress", "Email", "UserEmail", "Account", "SignedInUser", "UserPrincipalName", "SMTPAddress")
                                
                                foreach ($prop in $emailProperties) {
                                    if ($identity.$prop -and $identity.$prop -match "^[^@]+@[^@]+\.[^@]+$") {
                                        $email = $identity.$prop
                                        if ($allEmails -notcontains $email) {
                                            $allEmails += $email
                                            
                                            $accountInfo = @{
                                                EmailAddress = $email
                                                UserName = if ($identity.FriendlyName) { $identity.FriendlyName } elseif ($identity.DisplayName) { $identity.DisplayName } else { $email }
                                                TenantId = if ($identity.TenantId) { $identity.TenantId } else { "N/A" }
                                                OfficeVersion = $version
                                                AccountType = if ($identity.AccountType) { $identity.AccountType } else { "Registry" }
                                                IsSignedIn = if ($identity.IsSignedIn) { $identity.IsSignedIn } else { "Unknown" }
                                                Source = "Registry-$prop"
                                                Path = $key.PSPath
                                            }
                                            
                                            $officeInfo.Office365Accounts += $accountInfo
                                        }
                                    }
                                }
                                
                                # Verificar todas as propriedades que contenham email
                                if ($identity) {
                                    $identity.PSObject.Properties | ForEach-Object {
                                        if ($_.Value -and $_.Value -match "^[^@]+@[^@]+\.[^@]+$") {
                                            $email = $_.Value
                                            if ($allEmails -notcontains $email) {
                                                $allEmails += $email
                                                $officeInfo.Office365Accounts += @{
                                                    EmailAddress = $email
                                                    UserName = $email
                                                    TenantId = "N/A"
                                                    OfficeVersion = $version
                                                    AccountType = "Discovery"
                                                    Source = "Registry-" + $_.Name
                                                    Path = $key.PSPath
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch {
                            Write-Warning "Erro ao processar identidades: $($_.Exception.Message)"
                        }
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao verificar contas Office 365: $($_.Exception.Message)"
        }
        
        # Método 2: Verificar via Credential Manager
        try {
            $credentialAccounts = @()
            
            # Método usando cmdkey
            try {
                $credList = cmdkey /list 2>$null | Out-String
                $credentialTargets = @("office", "outlook", "microsoft", "live", "onedrive", "teams", "sharepoint", "onenote", "365")
                
                foreach ($target in $credentialTargets) {
                    $targetMatches = [regex]::Matches($credList, "Target: [^\r\n]*$target[^\r\n]*", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
                    foreach ($match in $targetMatches) {
                        $targetLine = $match.Value
                        # Procurar email no target
                        if ($targetLine -match "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})") {
                            $email = $matches[1]
                            if ($allEmails -notcontains $email) {
                                $allEmails += $email
                                $credentialAccounts += $email
                            }
                        }
                    }
                }
                
                # Procurar por linhas de usuário que contenham emails
                $userMatches = [regex]::Matches($credList, "User: ([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
                foreach ($match in $userMatches) {
                    $email = $match.Groups[1].Value
                    if ($allEmails -notcontains $email) {
                        $allEmails += $email
                        $credentialAccounts += $email
                    }
                }
            } catch {
                Write-Warning "Erro ao executar cmdkey: $($_.Exception.Message)"
            }
            
            if ($credentialAccounts.Count -gt 0) {
                $officeInfo.CredentialManagerAccounts = $credentialAccounts | Sort-Object -Unique
                foreach ($email in $credentialAccounts) {
                    $officeInfo.Office365Accounts += @{
                        EmailAddress = $email
                        UserName = $email
                        TenantId = "N/A"
                        OfficeVersion = "N/A"
                        AccountType = "Credential"
                        Source = "CredentialManager"
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao verificar Credential Manager: $($_.Exception.Message)"
        }
        
        # Método 3: Verificar logs do Office
        try {
            $logPaths = @(
                "$env:TEMP\*office*",
                "$env:LOCALAPPDATA\Microsoft\Office\ULS",
                "$env:APPDATA\Microsoft\Office\ULS",
                "$env:TEMP\Outlook Logging"
            )
            
            foreach ($logPath in $logPaths) {
                if (Test-Path $logPath) {
                    $logFiles = Get-ChildItem -Path $logPath -Recurse -Include "*.log", "*.txt" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 10
                    foreach ($file in $logFiles) {
                        try {
                            $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
                            if ($content) {
                                $emailMatches = [regex]::Matches($content, "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})")
                                foreach ($match in $emailMatches) {
                                    $email = $match.Groups[1].Value
                                    if ($allEmails -notcontains $email -and $email -notlike "*@microsoft.com" -and $email -notlike "*@office.com") {
                                        $allEmails += $email
                                        $officeInfo.Office365Accounts += @{
                                            EmailAddress = $email
                                            UserName = $email
                                            TenantId = "N/A"
                                            OfficeVersion = "N/A"
                                            AccountType = "LogFile"
                                            Source = "Log-" + $file.Name
                                        }
                                    }
                                }
                            }
                        } catch {
                            Write-Warning "Erro ao ler arquivo de log $($file.FullName): $($_.Exception.Message)"
                        }
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao verificar logs do Office: $($_.Exception.Message)"
        }
        
        # Método 4: Verificar contas através dos processos do Office em execução
        try {
            $officeProcesses = Get-Process | Where-Object { $_.ProcessName -match "OUTLOOK|WINWORD|EXCEL|POWERPNT|ONENOTE|MSACCESS|TEAMS" }
            if ($officeProcesses) {
                $officeInfo.RunningOfficeProcesses = $officeProcesses | ForEach-Object { 
                    @{
                        ProcessName = $_.ProcessName
                        Id = $_.Id
                        StartTime = if ($_.StartTime) { $_.StartTime.ToString("yyyy-MM-dd HH:mm:ss") } else { "N/A" }
                        WindowTitle = try { $_.MainWindowTitle } catch { "N/A" }
                    }
                }
                
                # Tentar extrair informações das janelas do Office
                foreach ($process in $officeProcesses) {
                    if ($process.MainWindowTitle -and $process.MainWindowTitle -match "([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})") {
                        $email = $matches[1]
                        if ($allEmails -notcontains $email) {
                            $allEmails += $email
                            $officeInfo.Office365Accounts += @{
                                EmailAddress = $email
                                UserName = $email
                                TenantId = "N/A"
                                OfficeVersion = "N/A"
                                AccountType = "ProcessWindow"
                                Source = "Process-" + $process.ProcessName
                            }
                        }
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao verificar processos do Office: $($_.Exception.Message)"
        }
        
        # Método 5: Verificar Office via OSPP.VBS
        try {
            $officePaths = @(
                "${env:ProgramFiles}\Microsoft Office\Office16",
                "${env:ProgramFiles}\Microsoft Office\Office15", 
                "${env:ProgramFiles}\Microsoft Office\Office14",
                "${env:ProgramFiles(x86)}\Microsoft Office\Office16",
                "${env:ProgramFiles(x86)}\Microsoft Office\Office15",
                "${env:ProgramFiles(x86)}\Microsoft Office\Office14"
            )
            
            foreach ($officePath in $officePaths) {
                $osppPath = Join-Path $officePath "ospp.vbs"
                if (Test-Path $osppPath) {
                    try {
                        # Executar ospp.vbs /dstatus para status detalhado
                        $osppOutput = & cscript //nologo $osppPath /dstatus 2>$null | Where-Object { $_ -ne "" }
                        
                        if ($osppOutput) {
                            $officeInfo.OSPPOutput = $osppOutput -join "`n"
                            
                            # Tentar extrair informações estruturadas
                            $products = @()
                            $currentProduct = @{}
                            
                            foreach ($line in $osppOutput) {
                                if ($line -match "PRODUCT ID:\s*(.+)") {
                                    if ($currentProduct.Count -gt 0) {
                                        $products += $currentProduct
                                    }
                                    $currentProduct = @{ ProductID = $matches[1].Trim() }
                                }
                                elseif ($line -match "SKU ID:\s*(.+)") {
                                    $currentProduct.SKUID = $matches[1].Trim()
                                }
                                elseif ($line -match "LICENSE NAME:\s*(.+)") {
                                    $currentProduct.LicenseName = $matches[1].Trim()
                                }
                                elseif ($line -match "LICENSE DESCRIPTION:\s*(.+)") {
                                    $currentProduct.LicenseDescription = $matches[1].Trim()
                                }
                                elseif ($line -match "LICENSE STATUS:\s*(.+)") {
                                    $currentProduct.LicenseStatus = $matches[1].Trim()
                                }
                                elseif ($line -match "ERROR CODE:\s*(.+)") {
                                    $currentProduct.ErrorCode = $matches[1].Trim()
                                }
                                elseif ($line -match "Last 5 characters of installed product key:\s*(.+)") {
                                    $currentProduct.PartialKey = $matches[1].Trim()
                                }
                            }
                            
                            if ($currentProduct.Count -gt 0) {
                                $products += $currentProduct
                            }
                            
                            $officeInfo.Products = $products
                        }
                        
                    } catch {
                        Write-Warning "Erro ao executar ospp.vbs: $($_.Exception.Message)"
                    }
                    break
                }
            }
        } catch {
            Write-Warning "Erro ao verificar OSPP: $($_.Exception.Message)"
        }
        
        # Método 6: Verificar Office Click-to-Run
        try {
            $c2rPath = "HKLM:\SOFTWARE\Microsoft\Office\ClickToRun\Configuration"
            if (Test-Path $c2rPath) {
                $c2rInfo = Get-ItemProperty -Path $c2rPath -ErrorAction SilentlyContinue
                if ($c2rInfo) {
                    $officeInfo.ClickToRun = @{
                        Platform = $c2rInfo.Platform
                        Channel = $c2rInfo.CDNBaseUrl
                        Version = $c2rInfo.VersionToReport
                        ProductIds = $c2rInfo.ProductReleaseIds
                        UpdateChannel = $c2rInfo.UpdateChannel
                        ClientCulture = $c2rInfo.ClientCulture
                    }
                }
            }
        } catch {
            Write-Warning "Erro ao verificar Click-to-Run: $($_.Exception.Message)"
        }
        
        return $officeInfo
    }
    catch {
        Write-Error "Erro ao obter informações do Office: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter informações do Office: $($_.Exception.Message)" }
    }
}

# Função para obter informações do sistema
function Get-SystemInfo {
    try {
        $systemInfo = @{}
        
        # Informações do computador
        $computerSystem = Get-CimInstance -ClassName Win32_ComputerSystem
        $systemInfo.ComputerName = $computerSystem.Name
        $systemInfo.Manufacturer = $computerSystem.Manufacturer
        $systemInfo.Model = $computerSystem.Model
        $systemInfo.TotalPhysicalMemoryGB = [math]::Round($computerSystem.TotalPhysicalMemory / 1GB, 2)
        
        # Informações do processador
        $processor = Get-CimInstance -ClassName Win32_Processor | Select-Object -First 1
        $systemInfo.Processor = @{
            Name = $processor.Name
            Cores = $processor.NumberOfCores
            LogicalProcessors = $processor.NumberOfLogicalProcessors
            MaxClockSpeed = $processor.MaxClockSpeed
            Architecture = $processor.Architecture
        }
        
        # Informações da placa-mãe
        $motherboard = Get-CimInstance -ClassName Win32_BaseBoard
        $systemInfo.Motherboard = @{
            Manufacturer = $motherboard.Manufacturer
            Product = $motherboard.Product
            SerialNumber = $motherboard.SerialNumber
            Version = $motherboard.Version
        }
        
        # Informações de memória RAM
        $memory = Get-CimInstance -ClassName Win32_PhysicalMemory
        $systemInfo.Memory = @()
        foreach ($mem in $memory) {
            $systemInfo.Memory += @{
                CapacityGB = [math]::Round($mem.Capacity / 1GB, 2)
                Speed = $mem.Speed
                Manufacturer = $mem.Manufacturer
                PartNumber = $mem.PartNumber
                SerialNumber = $mem.SerialNumber
            }
        }
        
        # Informações de armazenamento
        $drives = Get-CimInstance -ClassName Win32_DiskDrive
        $systemInfo.Storage = @()
        foreach ($drive in $drives) {
            $systemInfo.Storage += @{
                Model = $drive.Model
                SizeGB = [math]::Round($drive.Size / 1GB, 2)
                InterfaceType = $drive.InterfaceType
                SerialNumber = $drive.SerialNumber
                MediaType = $drive.MediaType
            }
        }
        
        # Informações da placa de vídeo
        $graphics = Get-CimInstance -ClassName Win32_VideoController
        $systemInfo.Graphics = @()
        foreach ($gpu in $graphics) {
            if ($gpu.Name -notlike "*Basic*" -and $gpu.Name -notlike "*VGA*") {
                $systemInfo.Graphics += @{
                    Name = $gpu.Name
                    AdapterRAMMB = if ($gpu.AdapterRAM) { [math]::Round($gpu.AdapterRAM / 1MB, 2) } else { "N/A" }
                    DriverVersion = $gpu.DriverVersion
                    DriverDate = $gpu.DriverDate
                }
            }
        }
        
        # Informações de rede
        $networkAdapters = Get-CimInstance -ClassName Win32_NetworkAdapter | Where-Object { $_.NetConnectionStatus -eq 2 }
        $systemInfo.Network = @()
        foreach ($adapter in $networkAdapters) {
            $systemInfo.Network += @{
                Name = $adapter.Name
                Manufacturer = $adapter.Manufacturer
                MACAddress = $adapter.MACAddress
                Speed = $adapter.Speed
            }
        }
        
        # Informações do BIOS
        $bios = Get-CimInstance -ClassName Win32_BIOS
        $systemInfo.BIOS = @{
            Manufacturer = $bios.Manufacturer
            Version = $bios.Version
            ReleaseDate = $bios.ReleaseDate
            SerialNumber = $bios.SerialNumber
        }
        
        return $systemInfo
    }
    catch {
        Write-Error "Erro ao obter informações do sistema: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter informações do sistema: $($_.Exception.Message)" }
    }
}

# Função para obter aplicativos instalados
function Get-InstalledApplications {
    try {
        $applications = @()
        
        # Registro do Windows para aplicativos instalados
        $registryPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*",
            "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*"
        )
        
        foreach ($path in $registryPaths) {
            try {
                $apps = Get-ItemProperty -Path $path -ErrorAction SilentlyContinue
                foreach ($app in $apps) {
                    if ($app.DisplayName -and $app.DisplayName.Trim() -ne "") {
                        $applications += @{
                            Name = $app.DisplayName
                            Version = if ($app.DisplayVersion) { $app.DisplayVersion } else { "N/A" }
                            Publisher = if ($app.Publisher) { $app.Publisher } else { "N/A" }
                            InstallDate = if ($app.InstallDate) { $app.InstallDate } else { "N/A" }
                            InstallLocation = if ($app.InstallLocation) { $app.InstallLocation } else { "N/A" }
                            UninstallString = if ($app.UninstallString) { $app.UninstallString } else { "N/A" }
                            EstimatedSizeMB = if ($app.EstimatedSize) { [math]::Round($app.EstimatedSize / 1024, 2) } else { "N/A" }
                        }
                    }
                }
            }
            catch {
                $errorMsg = $_.Exception.Message
                Write-Warning "Erro ao processar registry path ${path}: $errorMsg"
            }
        }
        
        # Remover duplicatas baseado no nome
        $uniqueApplications = $applications | Sort-Object Name | Group-Object Name | ForEach-Object { $_.Group | Select-Object -First 1 }
        
        return $uniqueApplications
    }
    catch {
        Write-Error "Erro ao obter aplicativos instalados: $($_.Exception.Message)"
        return @{ Error = "Erro ao obter aplicativos instalados: $($_.Exception.Message)" }
    }
}

# Função principal
function Get-CompleteSystemInfo {
    Write-Host "Coletando informações do sistema..." -ForegroundColor Green
    
    $systemData = @{
        CollectionDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        ComputerName = $env:COMPUTERNAME
        WindowsLicense = Get-WindowsLicenseInfo
        OfficeLicense = Get-OfficeLicenseInfo
        SystemInfo = Get-SystemInfo
        InstalledApplications = Get-InstalledApplications
    }
    
    return $systemData
}

# Executar coleta e converter para JSON
Write-Host "=== INICIANDO COLETA DE INFORMAÇÕES DO SISTEMA ===" -ForegroundColor Cyan
Write-Host ""

try {
    $completeInfo = Get-CompleteSystemInfo

    Write-Host ""
    Write-Host "=== CONVERTENDO PARA JSON ===" -ForegroundColor Cyan

    # Converter para JSON com formatação
    $jsonOutput = $completeInfo | ConvertTo-Json -Depth 10

    # Exibir resultado
    Write-Host ""
    Write-Host "=== RESULTADO EM JSON ===" -ForegroundColor Green
    Write-Host $jsonOutput

    # Salvar em arquivo com nome do computador
    $computerName = $env:COMPUTERNAME
    $outputFile = "$computerName.json"
    $jsonOutput | Out-File -FilePath $outputFile -Encoding UTF8

    Write-Host ""
    Write-Host "Informações salvas em: $outputFile" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "=== COLETA FINALIZADA ===" -ForegroundColor Cyan
}
catch {
    Write-Error "Erro durante a execução do script: $($_.Exception.Message)"
    Write-Host "Stack Trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}