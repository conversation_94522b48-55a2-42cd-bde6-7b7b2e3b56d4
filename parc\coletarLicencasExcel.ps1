# Script para detectar a licença ativa do Office via registro
# Autor: Script para identificação inteligente de licença Office

function Get-OfficeLicense {
    try {
        Write-Host "=== DETECTOR DE LICENÇA OFFICE ATIVA ===" -ForegroundColor Cyan
        Write-Host ""
        
        # Caminhos do registro
        $emailMappingPath = "HKCU:\Software\Microsoft\Office\16.0\Common\Licensing\LicensingNext\LicenseIdToEmailMapping"
        $cidMappingPath = "HKCU:\Software\Microsoft\Office\16.0\Common\Licensing\LicensingNext\CIDToLicenseIdsMapping"
        
        # Verificar se os caminhos existem
        if (!(Test-Path $emailMappingPath)) {
            Write-Host "❌ Caminho de mapeamento de emails não encontrado" -ForegroundColor Red
            Write-Host "Verifique se o Office está instalado e ativado" -ForegroundColor Yellow
            return
        }
        
        if (!(Test-Path $cidMappingPath)) {
            Write-Host "❌ Caminho de mapeamento CID não encontrado" -ForegroundColor Red
            Write-Host "Verifique se o Office está instalado e ativado" -ForegroundColor Yellow
            return
        }
        
        Write-Host "🔍 Coletando informações de licenciamento..." -ForegroundColor Yellow
        
        # Obter mapeamento LicenseId -> Email
        $emailMapping = @{}
        $emailProps = Get-ItemProperty -Path $emailMappingPath -ErrorAction SilentlyContinue
        
        if ($emailProps) {
            $emailProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                $licenseId = $_.Name
                $emailData = $_.Value
                
                # Parse do JSON se necessário
                try {
                    if ($emailData -like "*{*}*") {
                        $jsonData = $emailData | ConvertFrom-Json
                        $email = $jsonData.PSObject.Properties.Value | Select-Object -First 1
                    } else {
                        $email = $emailData
                    }
                    $emailMapping[$licenseId] = $email
                } catch {
                    $emailMapping[$licenseId] = $emailData
                }
            }
        }
        
        Write-Host "📧 Licenças encontradas:" -ForegroundColor Green
        $emailMapping.GetEnumerator() | ForEach-Object {
            Write-Host "  License ID: $($_.Key) -> Email: $($_.Value)" -ForegroundColor White
        }
        Write-Host ""
        
        # Obter CID ativo
        $activeLicenses = @()
        $cidProps = Get-ItemProperty -Path $cidMappingPath -ErrorAction SilentlyContinue
        
        if ($cidProps) {
            $cidProps.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                $cid = $_.Name
                $licenseData = $_.Value
                
                Write-Host "🔑 CID Ativo encontrado: $cid" -ForegroundColor Magenta
                
                # Verificar se este CID corresponde a algum License ID
                if ($emailMapping.ContainsKey($cid)) {
                    $activeEmail = $emailMapping[$cid]
                    $activeLicenses += [PSCustomObject]@{
                        CID = $cid
                        LicenseID = $cid
                        Email = $activeEmail
                        Status = "ATIVA"
                    }
                }
            }
        }
        
        Write-Host ""
        Write-Host "=== RESULTADO FINAL ===" -ForegroundColor Cyan
        
        if ($activeLicenses.Count -gt 0) {
            Write-Host "✅ Licença(s) ativa(s) encontrada(s):" -ForegroundColor Green
            $activeLicenses | ForEach-Object {
                Write-Host ""
                Write-Host "📍 LICENÇA ATIVA:" -ForegroundColor Green -BackgroundColor Black
                Write-Host "   Email de Ativação: $($_.Email)" -ForegroundColor White
                Write-Host "   License ID: $($_.LicenseID)" -ForegroundColor Gray
                Write-Host "   CID: $($_.CID)" -ForegroundColor Gray
                Write-Host "   Status: $($_.Status)" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ Nenhuma licença ativa encontrada" -ForegroundColor Red
            Write-Host "Possíveis causas:" -ForegroundColor Yellow
            Write-Host "  - Office não está ativado" -ForegroundColor Yellow
            Write-Host "  - Licença expirada" -ForegroundColor Yellow
            Write-Host "  - Versão diferente do Office (não 16.0)" -ForegroundColor Yellow
        }
        
        # Informações adicionais do sistema
        Write-Host ""
        Write-Host "=== INFORMAÇÕES DO SISTEMA ===" -ForegroundColor Cyan
        
        # Verificar versão do Office instalada
        $officeVersions = @("16.0", "15.0", "14.0")
        foreach ($version in $officeVersions) {
            $versionPath = "HKCU:\Software\Microsoft\Office\$version\Common\ProductVersion"
            if (Test-Path $versionPath) {
                $productVersion = Get-ItemProperty -Path $versionPath -ErrorAction SilentlyContinue
                if ($productVersion) {
                    Write-Host "📦 Office versão $version detectado" -ForegroundColor White
                }
            }
        }
        
        return $activeLicenses
        
    } catch {
        Write-Host "❌ Erro ao executar o script: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Verifique se você tem permissões para acessar o registro" -ForegroundColor Yellow
    }
}

# Função adicional para exportar resultado
function Export-LicenseInfo {
    param([array]$LicenseData)
    
    if ($LicenseData -and $LicenseData.Count -gt 0) {
        $exportPath = "$env:USERPROFILE\Desktop\Office_License_Info.txt"
        
        $output = @"
=== INFORMAÇÕES DE LICENÇA DO OFFICE ===
Data: $(Get-Date)
Computador: $env:COMPUTERNAME
Usuário: $env:USERNAME

"@
        
        $LicenseData | ForEach-Object {
            $output += @"
LICENÇA ATIVA:
Email de Ativação: $($_.Email)
License ID: $($_.LicenseID)
CID: $($_.CID)
Status: $($_.Status)

"@
        }
        
        $output | Out-File -FilePath $exportPath -Encoding UTF8
        Write-Host "📄 Informações exportadas para: $exportPath" -ForegroundColor Green
    }
}

# Executar a função principal
Write-Host "Iniciando detecção de licença do Office..." -ForegroundColor Cyan
$result = Get-OfficeLicense

# Perguntar se deseja exportar
if ($result -and $result.Count -gt 0) {
    Write-Host ""
    $export = Read-Host "Deseja exportar essas informações para um arquivo? (S/N)"
    if ($export -eq "S" -or $export -eq "s") {
        Export-LicenseInfo -LicenseData $result
    }
}

Write-Host ""
Write-Host "Script finalizado." -ForegroundColor Gray