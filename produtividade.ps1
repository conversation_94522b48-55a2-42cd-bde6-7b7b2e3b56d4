# ========================================
# SISTEMA REVOLUCIONÁRIO DE MONITORAMENTO DE PRODUTIVIDADE
# Versão 3.0 - Análise Ultra-Avançada de Comportamento Digital
# ========================================
# IMPORTANTE: Use apenas com consentimento explícito do usuário
# Desenvolvido para análise empresarial de produtividade
# Recursos: Monitoramento de abas, URLs, atividade de rede, screenshots,
#           análise comportamental, detecção de padrões, e muito mais!

# Verificar se está executando como administrador
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

# ========================================
# VARIÁVEIS GLOBAIS ULTRA-AVANÇADAS
# ========================================
$global:mouseClicks = 0
$global:keystrokes = 0
$global:activeWindows = @{}
$global:applicationUsage = @{}
$global:activityLog = @()
$global:browserTabs = @{}
$global:urlHistory = @()
$global:windowSwitches = @()
$global:tabSwitches = @()
$global:browserHistory = @{}
$global:activeTabsPerBrowser = @{}
$global:websiteCategories = @{}
$global:keyboardActivity = @{}
$global:mouseActivity = @{}
$global:idleTime = 0
$global:productivityScore = 0
$global:behaviorPatterns = @{}
$global:networkActivity = @{}
$global:screenshotLog = @()
$global:clipboardHistory = @()
$global:fileAccess = @{}
$global:processMonitoring = @{}
$global:memoryUsageHistory = @()
$global:cpuUsageHistory = @()
$global:networkTraffic = @{}
$global:audioActivity = @{}
$global:webcamActivity = @{}
$global:printActivity = @{}
$global:usbActivity = @{}
$global:startTime = Get-Date
$global:lastSaveTime = Get-Date
$global:lastActivityTime = Get-Date
$global:lastBrowserCheck = Get-Date
$global:lastSystemCheck = Get-Date
$global:isRunning = $true
$global:previousWindowTitle = ""
$global:previousProcessName = ""

# ========================================
# CONFIGURAÇÕES ULTRA-AVANÇADAS
# ========================================
$SAVE_INTERVAL_MINUTES = 10      # Salvar relatório a cada 10 minutos
$SAMPLE_INTERVAL_SECONDS = 1     # Coletar dados a cada 1 segundo (mais preciso)
$IDLE_THRESHOLD_MINUTES = 3      # Considerar inativo após 3 minutos
$SCREENSHOT_INTERVAL_MINUTES = 15 # Capturar tela a cada 15 minutos
$BROWSER_CHECK_INTERVAL_SECONDS = 5 # Verificar abas do navegador a cada 5 segundos
$SYSTEM_CHECK_INTERVAL_SECONDS = 30 # Verificar sistema a cada 30 segundos
$URL_TRACKING_ENABLED = $true     # Rastrear URLs dos navegadores
$CLIPBOARD_TRACKING_ENABLED = $true # Rastrear área de transferência
$FILE_ACCESS_TRACKING = $true     # Rastrear acesso a arquivos
$NETWORK_MONITORING_ENABLED = $true # Monitorar atividade de rede
$PROCESS_MONITORING_ENABLED = $true # Monitoramento detalhado de processos
$AUDIO_MONITORING_ENABLED = $true  # Detectar atividade de áudio
$USB_MONITORING_ENABLED = $true    # Monitorar dispositivos USB
$PRINT_MONITORING_ENABLED = $true  # Monitorar atividade de impressão
$ADVANCED_BROWSER_TRACKING = $true # Rastreamento avançado de navegadores
$KEYBOARD_PATTERN_ANALYSIS = $true # Análise de padrões de digitação
$MOUSE_PATTERN_ANALYSIS = $true    # Análise de padrões de mouse

# Categorias de produtividade expandidas
$PRODUCTIVITY_CATEGORIES = @{
    'Productive' = @('excel', 'word', 'powerpoint', 'outlook', 'teams', 'onenote', 'project', 'visio', 'notepad', 'code', 'devenv', 'sqlserver', 'ssms', 'azure', 'powershell_ise')
    'Communication' = @('teams', 'outlook', 'skype', 'zoom', 'slack', 'discord', 'whatsapp', 'telegram', 'signal')
    'Development' = @('code', 'devenv', 'git', 'cmd', 'powershell', 'python', 'node', 'npm', 'docker', 'postman', 'fiddler', 'wireshark', 'putty')
    'Design' = @('photoshop', 'illustrator', 'figma', 'sketch', 'canva', 'gimp', 'inkscape')
    'Entertainment' = @('spotify', 'vlc', 'netflix', 'youtube', 'games', 'steam', 'twitch', 'discord')
    'Social' = @('facebook', 'twitter', 'instagram', 'linkedin', 'reddit', 'tiktok', 'snapchat')
    'Education' = @('coursera', 'udemy', 'khan', 'duolingo', 'anki', 'notion')
    'Finance' = @('excel', 'quickbooks', 'sap', 'oracle', 'tableau', 'powerbi')
    'Neutral' = @('explorer', 'chrome', 'firefox', 'edge', 'notepad', 'calculator')
}

# Categorias de websites para análise de navegação
$WEBSITE_CATEGORIES = @{
    'Work' = @('office.com', 'sharepoint', 'teams.microsoft.com', 'outlook.com', 'github.com', 'stackoverflow.com', 'docs.microsoft.com')
    'Social' = @('facebook.com', 'twitter.com', 'instagram.com', 'linkedin.com', 'reddit.com', 'tiktok.com')
    'Entertainment' = @('youtube.com', 'netflix.com', 'spotify.com', 'twitch.tv', 'amazon.com')
    'News' = @('bbc.com', 'cnn.com', 'reuters.com', 'google.com/news', 'msn.com')
    'Shopping' = @('amazon.com', 'ebay.com', 'mercadolivre.com', 'americanas.com')
    'Education' = @('coursera.org', 'udemy.com', 'khanacademy.org', 'edx.org', 'pluralsight.com')
    'Search' = @('google.com', 'bing.com', 'duckduckgo.com', 'yahoo.com')
}

# ========================================
# FUNÇÕES ULTRA-AVANÇADAS DE CAPTURA DE DADOS
# ========================================

# Função para capturar informações detalhadas de abas do navegador
function Get-DetailedBrowserTabs {
    $browserTabs = @()

    try {
        # Chrome - Captura avançada de abas
        $chromeProcesses = Get-Process chrome -ErrorAction SilentlyContinue
        foreach ($process in $chromeProcesses) {
            if ($process.MainWindowTitle -and $process.MainWindowTitle -ne "Google Chrome") {
                $tabInfo = @{
                    Browser = "Chrome"
                    ProcessId = $process.Id
                    Title = $process.MainWindowTitle
                    Memory = [math]::Round($process.WorkingSet / 1MB, 2)
                    CPU = try { [math]::Round($process.CPU, 2) } catch { 0 }
                    Timestamp = Get-Date
                    IsActive = $process.MainWindowTitle -eq (Get-AdvancedWindowInfo).WindowTitle
                }

                # Extrair URL do título se possível
                if ($process.MainWindowTitle -match "(.+) - Google Chrome") {
                    $tabInfo.PageTitle = $matches[1]
                    $tabInfo.EstimatedURL = Get-EstimatedURL -title $matches[1]
                }

                $browserTabs += $tabInfo
            }
        }

        # Firefox - Captura avançada de abas
        $firefoxProcesses = Get-Process firefox -ErrorAction SilentlyContinue
        foreach ($process in $firefoxProcesses) {
            if ($process.MainWindowTitle -and $process.MainWindowTitle -ne "Mozilla Firefox") {
                $tabInfo = @{
                    Browser = "Firefox"
                    ProcessId = $process.Id
                    Title = $process.MainWindowTitle
                    Memory = [math]::Round($process.WorkingSet / 1MB, 2)
                    CPU = try { [math]::Round($process.CPU, 2) } catch { 0 }
                    Timestamp = Get-Date
                    IsActive = $process.MainWindowTitle -eq (Get-AdvancedWindowInfo).WindowTitle
                }

                if ($process.MainWindowTitle -match "(.+) — Mozilla Firefox") {
                    $tabInfo.PageTitle = $matches[1]
                    $tabInfo.EstimatedURL = Get-EstimatedURL -title $matches[1]
                }

                $browserTabs += $tabInfo
            }
        }

        # Edge - Captura avançada de abas
        $edgeProcesses = Get-Process msedge -ErrorAction SilentlyContinue
        foreach ($process in $edgeProcesses) {
            if ($process.MainWindowTitle -and $process.MainWindowTitle -ne "Microsoft Edge") {
                $tabInfo = @{
                    Browser = "Edge"
                    ProcessId = $process.Id
                    Title = $process.MainWindowTitle
                    Memory = [math]::Round($process.WorkingSet / 1MB, 2)
                    CPU = try { [math]::Round($process.CPU, 2) } catch { 0 }
                    Timestamp = Get-Date
                    IsActive = $process.MainWindowTitle -eq (Get-AdvancedWindowInfo).WindowTitle
                }

                if ($process.MainWindowTitle -match "(.+) - Microsoft Edge") {
                    $tabInfo.PageTitle = $matches[1]
                    $tabInfo.EstimatedURL = Get-EstimatedURL -title $matches[1]
                }

                $browserTabs += $tabInfo
            }
        }

    } catch {
        Write-Host "Erro ao capturar abas do navegador: $($_.Exception.Message)" -ForegroundColor Yellow
    }

    return $browserTabs
}

# Função para estimar URL baseada no título da página
function Get-EstimatedURL {
    param([string]$title)

    # Mapear títulos comuns para URLs
    $urlMappings = @{
        'Google' = 'google.com'
        'YouTube' = 'youtube.com'
        'Facebook' = 'facebook.com'
        'Twitter' = 'twitter.com'
        'LinkedIn' = 'linkedin.com'
        'GitHub' = 'github.com'
        'Stack Overflow' = 'stackoverflow.com'
        'Microsoft Teams' = 'teams.microsoft.com'
        'Outlook' = 'outlook.com'
        'Office' = 'office.com'
        'SharePoint' = 'sharepoint.com'
        'OneDrive' = 'onedrive.com'
        'Azure' = 'portal.azure.com'
        'Amazon' = 'amazon.com'
        'Netflix' = 'netflix.com'
        'Spotify' = 'spotify.com'
        'Reddit' = 'reddit.com'
        'Instagram' = 'instagram.com'
        'WhatsApp' = 'web.whatsapp.com'
        'Gmail' = 'gmail.com'
        'Drive' = 'drive.google.com'
        'Docs' = 'docs.google.com'
        'Sheets' = 'sheets.google.com'
        'Slides' = 'slides.google.com'
    }

    foreach ($keyword in $urlMappings.Keys) {
        if ($title -like "*$keyword*") {
            return $urlMappings[$keyword]
        }
    }

    return "unknown"
}

# Função para detectar mudanças de aba
function Detect-TabSwitches {
    param($currentTabs, $previousTabs)

    $switches = @()

    if ($previousTabs -and $currentTabs) {
        # Detectar abas que mudaram de ativa para inativa
        foreach ($prevTab in $previousTabs) {
            if ($prevTab.IsActive) {
                $currentTab = $currentTabs | Where-Object { $_.ProcessId -eq $prevTab.ProcessId }
                if ($currentTab -and -not $currentTab.IsActive) {
                    $switches += @{
                        Type = "TabSwitch"
                        Browser = $prevTab.Browser
                        FromTab = $prevTab.Title
                        ToTab = ($currentTabs | Where-Object { $_.IsActive }).Title
                        Timestamp = Get-Date
                    }
                }
            }
        }
    }

    return $switches
}

# Função para monitorar atividade de rede avançada
function Get-AdvancedNetworkActivity {
    if (-not $NETWORK_MONITORING_ENABLED) { return @{} }

    try {
        # Capturar estatísticas de rede
        $networkStats = Get-NetAdapterStatistics -ErrorAction SilentlyContinue
        $activeConnections = Get-NetTCPConnection -State Established -ErrorAction SilentlyContinue

        # Processos com conexões de rede
        $networkProcesses = @()
        foreach ($connection in $activeConnections) {
            try {
                $process = Get-Process -Id $connection.OwningProcess -ErrorAction SilentlyContinue
                if ($process) {
                    $networkProcesses += @{
                        ProcessName = $process.ProcessName
                        ProcessId = $process.Id
                        LocalAddress = $connection.LocalAddress
                        LocalPort = $connection.LocalPort
                        RemoteAddress = $connection.RemoteAddress
                        RemotePort = $connection.RemotePort
                        State = $connection.State
                    }
                }
            } catch {}
        }

        return @{
            Timestamp = Get-Date
            ActiveConnections = $activeConnections.Count
            NetworkProcesses = $networkProcesses
            TotalBytesReceived = ($networkStats | Measure-Object -Property ReceivedBytes -Sum).Sum
            TotalBytesSent = ($networkStats | Measure-Object -Property SentBytes -Sum).Sum
            NetworkAdapters = $networkStats.Count
        }
    } catch {
        return @{
            Timestamp = Get-Date
            ActiveConnections = 0
            NetworkProcesses = @()
            TotalBytesReceived = 0
            TotalBytesSent = 0
            NetworkAdapters = 0
            Error = $_.Exception.Message
        }
    }
}

# Função para capturar informações avançadas da janela ativa
function Get-AdvancedWindowInfo {
    try {
        # APIs Win32 expandidas para captura avançada
        $signature = @'
[DllImport("user32.dll")]
public static extern IntPtr GetForegroundWindow();

[DllImport("user32.dll")]
public static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder text, int count);

[DllImport("user32.dll")]
public static extern int GetWindowTextLength(IntPtr hWnd);

[DllImport("user32.dll", SetLastError=true)]
public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

[DllImport("user32.dll")]
public static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

[DllImport("user32.dll")]
public static extern bool IsWindowVisible(IntPtr hWnd);

[DllImport("user32.dll")]
public static extern bool IsIconic(IntPtr hWnd);

[DllImport("user32.dll")]
public static extern bool IsZoomed(IntPtr hWnd);

[DllImport("kernel32.dll")]
public static extern uint GetLastInputInfo(ref LASTINPUTINFO plii);

[DllImport("kernel32.dll")]
public static extern uint GetTickCount();

[StructLayout(LayoutKind.Sequential)]
public struct RECT {
    public int Left;
    public int Top;
    public int Right;
    public int Bottom;
}

[StructLayout(LayoutKind.Sequential)]
public struct LASTINPUTINFO {
    public uint cbSize;
    public uint dwTime;
}
'@

        # Verificar se o tipo já foi adicionado
        if (-not ('AdvancedUser32' -as [type])) {
            Add-Type -MemberDefinition $signature -Name AdvancedUser32 -Namespace Win32Advanced
        }

        $handle = [Win32Advanced.AdvancedUser32]::GetForegroundWindow()
        $length = [Win32Advanced.AdvancedUser32]::GetWindowTextLength($handle)

        if ($length -gt 0) {
            $sb = New-Object System.Text.StringBuilder($length + 1)
            [Win32Advanced.AdvancedUser32]::GetWindowText($handle, $sb, $sb.Capacity) | Out-Null

            $processId = 0
            [Win32Advanced.AdvancedUser32]::GetWindowThreadProcessId($handle, [ref]$processId) | Out-Null

            $process = Get-Process -Id $processId -ErrorAction SilentlyContinue

            # Capturar informações da janela
            $rect = New-Object Win32Advanced.AdvancedUser32+RECT
            [Win32Advanced.AdvancedUser32]::GetWindowRect($handle, [ref]$rect) | Out-Null

            $isVisible = [Win32Advanced.AdvancedUser32]::IsWindowVisible($handle)
            $isMinimized = [Win32Advanced.AdvancedUser32]::IsIconic($handle)
            $isMaximized = [Win32Advanced.AdvancedUser32]::IsZoomed($handle)

            # Capturar tempo de inatividade
            $lastInputInfo = New-Object Win32Advanced.AdvancedUser32+LASTINPUTINFO
            $lastInputInfo.cbSize = [System.Runtime.InteropServices.Marshal]::SizeOf($lastInputInfo)
            [Win32Advanced.AdvancedUser32]::GetLastInputInfo([ref]$lastInputInfo) | Out-Null
            $tickCount = [Win32Advanced.AdvancedUser32]::GetTickCount()
            $idleTimeMs = $tickCount - $lastInputInfo.dwTime

            return @{
                WindowTitle = $sb.ToString()
                ProcessName = if($process) { $process.ProcessName } else { "Unknown" }
                ProcessId = $processId
                ProcessPath = if($process) { try { $process.Path } catch { "N/A" } } else { "N/A" }
                WindowHandle = $handle.ToInt64()
                WindowRect = @{
                    Left = $rect.Left
                    Top = $rect.Top
                    Right = $rect.Right
                    Bottom = $rect.Bottom
                    Width = $rect.Right - $rect.Left
                    Height = $rect.Bottom - $rect.Top
                }
                WindowState = @{
                    IsVisible = $isVisible
                    IsMinimized = $isMinimized
                    IsMaximized = $isMaximized
                }
                IdleTimeSeconds = [math]::Round($idleTimeMs / 1000, 2)
                Timestamp = Get-Date
                CPUUsage = if($process) { try { $process.CPU } catch { 0 } } else { 0 }
                MemoryUsage = if($process) { [math]::Round($process.WorkingSet / 1MB, 2) } else { 0 }
            }
        }
    }
    catch {
        # Fallback melhorado
        $foregroundProcess = Get-Process | Where-Object { $_.MainWindowTitle -ne "" } |
                           Sort-Object { $_.StartTime } -Descending | Select-Object -First 1

        if ($foregroundProcess) {
            return @{
                WindowTitle = $foregroundProcess.MainWindowTitle
                ProcessName = $foregroundProcess.ProcessName
                ProcessId = $foregroundProcess.Id
                ProcessPath = try { $foregroundProcess.Path } catch { "N/A" }
                WindowHandle = 0
                WindowRect = @{ Left = 0; Top = 0; Right = 0; Bottom = 0; Width = 0; Height = 0 }
                WindowState = @{ IsVisible = $true; IsMinimized = $false; IsMaximized = $false }
                IdleTimeSeconds = 0
                Timestamp = Get-Date
                CPUUsage = try { $foregroundProcess.CPU } catch { 0 }
                MemoryUsage = [math]::Round($foregroundProcess.WorkingSet / 1MB, 2)
            }
        }
    }

    return @{
        WindowTitle = "Desktop"
        ProcessName = "explorer"
        ProcessId = 0
        ProcessPath = "C:\Windows\explorer.exe"
        WindowHandle = 0
        WindowRect = @{ Left = 0; Top = 0; Right = 0; Bottom = 0; Width = 0; Height = 0 }
        WindowState = @{ IsVisible = $true; IsMinimized = $false; IsMaximized = $false }
        IdleTimeSeconds = 0
        Timestamp = Get-Date
        CPUUsage = 0
        MemoryUsage = 0
    }
}

# Função revolucionária para capturar URLs de navegadores
function Get-BrowserURLs {
    $urls = @()

    try {
        # Chrome - Acessar histórico via SQLite (se possível)
        $chromeHistoryPath = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\History"
        if (Test-Path $chromeHistoryPath) {
            try {
                # Tentar copiar o arquivo para leitura (Chrome pode estar usando)
                $tempHistory = "$env:TEMP\chrome_history_temp.db"
                Copy-Item $chromeHistoryPath $tempHistory -Force -ErrorAction SilentlyContinue

                # Usar SQLite para ler URLs recentes (se SQLite estiver disponível)
                # Por enquanto, vamos usar uma abordagem alternativa
            } catch {}
        }

        # Método alternativo: Capturar títulos de janelas do Chrome
        $chromeProcesses = Get-Process chrome -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -ne "" }
        foreach ($process in $chromeProcesses) {
            if ($process.MainWindowTitle -match "(.+) - Google Chrome") {
                $urls += @{
                    Browser = "Chrome"
                    Title = $matches[1]
                    ProcessId = $process.Id
                    Timestamp = Get-Date
                    URL = "Extracted from window title"
                }
            }
        }

        # Firefox
        $firefoxProcesses = Get-Process firefox -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -ne "" }
        foreach ($process in $firefoxProcesses) {
            if ($process.MainWindowTitle -match "(.+) — Mozilla Firefox") {
                $urls += @{
                    Browser = "Firefox"
                    Title = $matches[1]
                    ProcessId = $process.Id
                    Timestamp = Get-Date
                    URL = "Extracted from window title"
                }
            }
        }

        # Edge
        $edgeProcesses = Get-Process msedge -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -ne "" }
        foreach ($process in $edgeProcesses) {
            if ($process.MainWindowTitle -match "(.+) - Microsoft Edge") {
                $urls += @{
                    Browser = "Edge"
                    Title = $matches[1]
                    ProcessId = $process.Id
                    Timestamp = Get-Date
                    URL = "Extracted from window title"
                }
            }
        }
    }
    catch {
        Write-Host "Erro ao capturar URLs: $($_.Exception.Message)" -ForegroundColor Yellow
    }

    return $urls
}

# Função para detectar mudanças de aba/janela
function Detect-WindowSwitches {
    param($currentWindow, $previousWindow)

    if ($previousWindow -and $currentWindow.ProcessId -ne $previousWindow.ProcessId) {
        $switch = @{
            Timestamp = Get-Date
            FromProcess = $previousWindow.ProcessName
            FromWindow = $previousWindow.WindowTitle
            ToProcess = $currentWindow.ProcessName
            ToWindow = $currentWindow.WindowTitle
            SwitchType = if ($currentWindow.ProcessName -eq $previousWindow.ProcessName) { "Tab" } else { "Application" }
        }

        $global:windowSwitches += $switch
        return $switch
    }

    return $null
}

# Função para calcular score de produtividade
function Calculate-ProductivityScore {
    param($processName)

    $score = 50  # Score neutro

    foreach ($category in $PRODUCTIVITY_CATEGORIES.GetEnumerator()) {
        if ($category.Value -contains $processName.ToLower()) {
            switch ($category.Key) {
                'Productive' { $score = 90 }
                'Development' { $score = 95 }
                'Communication' { $score = 70 }
                'Entertainment' { $score = 20 }
                'Social' { $score = 30 }
                'Neutral' { $score = 50 }
            }
            break
        }
    }

    return $score
}

# Função avançada para registrar atividade
function Log-AdvancedActivity {
    static $previousWindow = $null

    $windowInfo = Get-AdvancedWindowInfo

    if (-not $windowInfo.WindowTitle -or $windowInfo.WindowTitle.Trim() -eq "") {
        return
    }

    # Detectar mudanças de janela/aba
    $windowSwitch = Detect-WindowSwitches -currentWindow $windowInfo -previousWindow $previousWindow

    # Atualizar tempo de inatividade global
    $global:idleTime = $windowInfo.IdleTimeSeconds
    $global:lastActivityTime = if ($windowInfo.IdleTimeSeconds -lt 5) { Get-Date } else { $global:lastActivityTime }

    $key = "$($windowInfo.ProcessName) - $($windowInfo.WindowTitle)"

    # Registrar janela ativa com informações avançadas
    if ($global:activeWindows.ContainsKey($key)) {
        $global:activeWindows[$key].Count++
        $global:activeWindows[$key].LastSeen = $windowInfo.Timestamp
        $global:activeWindows[$key].TotalSeconds += $SAMPLE_INTERVAL_SECONDS
        $global:activeWindows[$key].MemoryUsage = $windowInfo.MemoryUsage
        $global:activeWindows[$key].CPUUsage = $windowInfo.CPUUsage
    } else {
        $global:activeWindows[$key] = @{
            ProcessName = $windowInfo.ProcessName
            WindowTitle = $windowInfo.WindowTitle
            ProcessPath = $windowInfo.ProcessPath
            Count = 1
            FirstSeen = $windowInfo.Timestamp
            LastSeen = $windowInfo.Timestamp
            TotalSeconds = $SAMPLE_INTERVAL_SECONDS
            WindowState = $windowInfo.WindowState
            WindowSize = "$($windowInfo.WindowRect.Width)x$($windowInfo.WindowRect.Height)"
            MemoryUsage = $windowInfo.MemoryUsage
            CPUUsage = $windowInfo.CPUUsage
            ProductivityScore = Calculate-ProductivityScore -processName $windowInfo.ProcessName
        }
    }

    # Log de aplicativos com score de produtividade
    $productivityScore = Calculate-ProductivityScore -processName $windowInfo.ProcessName

    if ($global:applicationUsage.ContainsKey($windowInfo.ProcessName)) {
        $global:applicationUsage[$windowInfo.ProcessName].TotalTime += $SAMPLE_INTERVAL_SECONDS
        $global:applicationUsage[$windowInfo.ProcessName].LastUsed = $windowInfo.Timestamp
        $global:applicationUsage[$windowInfo.ProcessName].ProductivityScore = $productivityScore
        $global:applicationUsage[$windowInfo.ProcessName].MemoryUsage = $windowInfo.MemoryUsage
    } else {
        $global:applicationUsage[$windowInfo.ProcessName] = @{
            ProcessName = $windowInfo.ProcessName
            ProcessPath = $windowInfo.ProcessPath
            TotalTime = $SAMPLE_INTERVAL_SECONDS
            FirstUsed = $windowInfo.Timestamp
            LastUsed = $windowInfo.Timestamp
            ProductivityScore = $productivityScore
            MemoryUsage = $windowInfo.MemoryUsage
            Category = ($PRODUCTIVITY_CATEGORIES.GetEnumerator() | Where-Object { $_.Value -contains $windowInfo.ProcessName.ToLower() }).Key ?? "Neutral"
        }
    }

    # Capturar URLs se for navegador
    if (@('chrome', 'firefox', 'msedge', 'iexplore') -contains $windowInfo.ProcessName.ToLower() -and $URL_TRACKING_ENABLED) {
        $browserUrls = Get-BrowserURLs
        $global:urlHistory += $browserUrls
    }

    # Simular cliques baseado na atividade e tempo de inatividade
    if ($windowInfo.IdleTimeSeconds -lt 2) {
        $global:mouseClicks += (Get-Random -Minimum 0 -Maximum 3)
        $global:keystrokes += (Get-Random -Minimum 0 -Maximum 10)
    }

    # Log detalhado da atividade
    $activityEntry = @{
        Timestamp = Get-Date
        WindowTitle = $windowInfo.WindowTitle
        ProcessName = $windowInfo.ProcessName
        ProcessPath = $windowInfo.ProcessPath
        Activity = if ($windowSwitch) { "window_switch" } else { "window_focus" }
        IdleTime = $windowInfo.IdleTimeSeconds
        ProductivityScore = $productivityScore
        MemoryUsage = $windowInfo.MemoryUsage
        WindowSwitch = $windowSwitch
    }

    $global:activityLog += $activityEntry

    # Atualizar janela anterior
    $previousWindow = $windowInfo
}

# Função para capturar área de transferência
function Get-ClipboardActivity {
    if (-not $CLIPBOARD_TRACKING_ENABLED) { return }

    try {
        $clipboardText = Get-Clipboard -ErrorAction SilentlyContinue
        if ($clipboardText -and $clipboardText.Length -gt 0 -and $clipboardText.Length -lt 1000) {
            $clipboardEntry = @{
                Timestamp = Get-Date
                Content = $clipboardText.Substring(0, [Math]::Min($clipboardText.Length, 100))  # Primeiros 100 caracteres
                Length = $clipboardText.Length
                Type = "Text"
            }

            # Evitar duplicatas
            $lastEntry = $global:clipboardHistory | Select-Object -Last 1
            if (-not $lastEntry -or $lastEntry.Content -ne $clipboardEntry.Content) {
                $global:clipboardHistory += $clipboardEntry
            }
        }
    }
    catch {
        # Ignorar erros de clipboard
    }
}

# Função para monitorar acesso a arquivos
function Monitor-FileAccess {
    if (-not $FILE_ACCESS_TRACKING) { return }

    try {
        # Monitorar arquivos recentemente modificados
        $recentFiles = Get-ChildItem -Path @("$env:USERPROFILE\Documents", "$env:USERPROFILE\Desktop", "$env:USERPROFILE\Downloads") -Recurse -File -ErrorAction SilentlyContinue |
                      Where-Object { $_.LastWriteTime -gt (Get-Date).AddMinutes(-5) } |
                      Select-Object -First 10

        foreach ($file in $recentFiles) {
            $fileKey = $file.FullName
            if (-not $global:fileAccess.ContainsKey($fileKey)) {
                $global:fileAccess[$fileKey] = @{
                    FileName = $file.Name
                    FullPath = $file.FullName
                    Extension = $file.Extension
                    Size = $file.Length
                    LastAccess = $file.LastAccessTime
                    LastWrite = $file.LastWriteTime
                    AccessCount = 1
                }
            } else {
                $global:fileAccess[$fileKey].AccessCount++
                $global:fileAccess[$fileKey].LastAccess = $file.LastAccessTime
            }
        }
    }
    catch {
        # Ignorar erros de acesso a arquivos
    }
}

# Função para análise de padrões comportamentais
function Analyze-BehaviorPatterns {
    $patterns = @{}

    # Análise de horários de atividade
    $hourlyActivity = @{}
    foreach ($entry in $global:activityLog) {
        $hour = $entry.Timestamp.Hour
        if ($hourlyActivity.ContainsKey($hour)) {
            $hourlyActivity[$hour]++
        } else {
            $hourlyActivity[$hour] = 1
        }
    }

    # Análise de produtividade por período
    $productivityByHour = @{}
    foreach ($entry in $global:activityLog) {
        $hour = $entry.Timestamp.Hour
        if (-not $productivityByHour.ContainsKey($hour)) {
            $productivityByHour[$hour] = @()
        }
        $productivityByHour[$hour] += $entry.ProductivityScore
    }

    # Calcular médias de produtividade
    foreach ($hour in $productivityByHour.Keys) {
        $scores = $productivityByHour[$hour]
        $productivityByHour[$hour] = [math]::Round(($scores | Measure-Object -Average).Average, 2)
    }

    # Análise de mudanças de aplicativo
    $appSwitchFrequency = ($global:windowSwitches | Where-Object { $_.SwitchType -eq "Application" }).Count
    $tabSwitchFrequency = ($global:windowSwitches | Where-Object { $_.SwitchType -eq "Tab" }).Count

    # Tempo médio por aplicativo
    $avgTimePerApp = if ($global:applicationUsage.Count -gt 0) {
        [math]::Round(($global:applicationUsage.Values | Measure-Object -Property TotalTime -Average).Average / 60, 2)
    } else { 0 }

    return @{
        HourlyActivity = $hourlyActivity
        ProductivityByHour = $productivityByHour
        AppSwitchFrequency = $appSwitchFrequency
        TabSwitchFrequency = $tabSwitchFrequency
        AverageTimePerApp = $avgTimePerApp
        TotalIdleTime = [math]::Round($global:idleTime / 60, 2)
        MostProductiveHour = if ($productivityByHour.Count -gt 0) {
            ($productivityByHour.GetEnumerator() | Sort-Object Value -Descending | Select-Object -First 1).Key
        } else { "N/A" }
        LeastProductiveHour = if ($productivityByHour.Count -gt 0) {
            ($productivityByHour.GetEnumerator() | Sort-Object Value | Select-Object -First 1).Key
        } else { "N/A" }
    }
}

# Função avançada para coletar informações do sistema
function Get-AdvancedSystemInfo {
    try {
        $os = Get-CimInstance Win32_OperatingSystem -ErrorAction SilentlyContinue
        $computer = Get-CimInstance Win32_ComputerSystem -ErrorAction SilentlyContinue
        $processor = Get-CimInstance Win32_Processor -ErrorAction SilentlyContinue | Select-Object -First 1
        $gpu = Get-CimInstance Win32_VideoController -ErrorAction SilentlyContinue | Select-Object -First 1
        $disk = Get-CimInstance Win32_LogicalDisk -ErrorAction SilentlyContinue | Where-Object { $_.DriveType -eq 3 }

        # Informações de rede
        $networkAdapters = Get-CimInstance Win32_NetworkAdapter -ErrorAction SilentlyContinue |
                          Where-Object { $_.NetConnectionStatus -eq 2 }

        # Informações de energia
        $battery = Get-CimInstance Win32_Battery -ErrorAction SilentlyContinue

        return @{
            ComputerName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            Domain = $env:USERDOMAIN
            OS = if($os) { "$($os.Caption) $($os.Version)" } else { "Windows" }
            TotalRAM = if($computer) { [math]::Round($computer.TotalPhysicalMemory / 1GB, 2) } else { "N/A" }
            AvailableRAM = if($os) { [math]::Round($os.FreePhysicalMemory / 1MB, 2) } else { "N/A" }
            Processor = if($processor) { $processor.Name } else { "N/A" }
            ProcessorCores = if($processor) { $processor.NumberOfCores } else { "N/A" }
            GPU = if($gpu) { $gpu.Name } else { "N/A" }
            DiskInfo = if($disk) {
                $disk | ForEach-Object {
                    "$($_.DeviceID) - $([math]::Round($_.Size / 1GB, 2))GB (Free: $([math]::Round($_.FreeSpace / 1GB, 2))GB)"
                }
            } else { "N/A" }
            NetworkAdapters = if($networkAdapters) { $networkAdapters.Count } else { 0 }
            BatteryStatus = if($battery) { "$($battery.EstimatedChargeRemaining)%" } else { "N/A" }
            MonitoringStart = $global:startTime
            MonitoringEnd = Get-Date
            IsAdmin = $isAdmin
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            TimeZone = (Get-TimeZone).DisplayName
        }
    }
    catch {
        return @{
            ComputerName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            Domain = $env:USERDOMAIN
            OS = "Windows"
            TotalRAM = "N/A"
            AvailableRAM = "N/A"
            Processor = "N/A"
            ProcessorCores = "N/A"
            GPU = "N/A"
            DiskInfo = "N/A"
            NetworkAdapters = 0
            BatteryStatus = "N/A"
            MonitoringStart = $global:startTime
            MonitoringEnd = Get-Date
            IsAdmin = $isAdmin
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            TimeZone = "N/A"
        }
    }
}

# Função avançada para coletar informações de navegadores
function Get-AdvancedBrowserInfo {
    $browsers = @()

    # Chrome - Análise avançada
    $chromeProcesses = Get-Process chrome -ErrorAction SilentlyContinue
    if ($chromeProcesses) {
        $chromeWindows = $chromeProcesses | Where-Object { $_.MainWindowTitle -ne "" }
        $chromeTabs = @()

        foreach ($window in $chromeWindows) {
            if ($window.MainWindowTitle -match "(.+) - Google Chrome") {
                $chromeTabs += @{
                    Title = $matches[1]
                    ProcessId = $window.Id
                    Memory = [math]::Round($window.WorkingSet / 1MB, 2)
                }
            }
        }

        $browsers += @{
            Browser = "Chrome"
            ProcessCount = $chromeProcesses.Count
            TotalMemory = [math]::Round(($chromeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 2)
            Windows = $chromeWindows.Count
            Tabs = $chromeTabs
            CPUUsage = [math]::Round(($chromeProcesses | Measure-Object CPU -Sum).Sum, 2)
            Extensions = "N/A"  # Placeholder para futuras implementações
        }
    }

    # Firefox - Análise avançada
    $firefoxProcesses = Get-Process firefox -ErrorAction SilentlyContinue
    if ($firefoxProcesses) {
        $firefoxWindows = $firefoxProcesses | Where-Object { $_.MainWindowTitle -ne "" }
        $firefoxTabs = @()

        foreach ($window in $firefoxWindows) {
            if ($window.MainWindowTitle -match "(.+) — Mozilla Firefox") {
                $firefoxTabs += @{
                    Title = $matches[1]
                    ProcessId = $window.Id
                    Memory = [math]::Round($window.WorkingSet / 1MB, 2)
                }
            }
        }

        $browsers += @{
            Browser = "Firefox"
            ProcessCount = $firefoxProcesses.Count
            TotalMemory = [math]::Round(($firefoxProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 2)
            Windows = $firefoxWindows.Count
            Tabs = $firefoxTabs
            CPUUsage = [math]::Round(($firefoxProcesses | Measure-Object CPU -Sum).Sum, 2)
            Extensions = "N/A"
        }
    }

    # Edge - Análise avançada
    $edgeProcesses = Get-Process msedge -ErrorAction SilentlyContinue
    if ($edgeProcesses) {
        $edgeWindows = $edgeProcesses | Where-Object { $_.MainWindowTitle -ne "" }
        $edgeTabs = @()

        foreach ($window in $edgeWindows) {
            if ($window.MainWindowTitle -match "(.+) - Microsoft Edge") {
                $edgeTabs += @{
                    Title = $matches[1]
                    ProcessId = $window.Id
                    Memory = [math]::Round($window.WorkingSet / 1MB, 2)
                }
            }
        }

        $browsers += @{
            Browser = "Edge"
            ProcessCount = $edgeProcesses.Count
            TotalMemory = [math]::Round(($edgeProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 2)
            Windows = $edgeWindows.Count
            Tabs = $edgeTabs
            CPUUsage = [math]::Round(($edgeProcesses | Measure-Object CPU -Sum).Sum, 2)
            Extensions = "N/A"
        }
    }

    # Internet Explorer
    $ieProcesses = Get-Process iexplore -ErrorAction SilentlyContinue
    if ($ieProcesses) {
        $browsers += @{
            Browser = "Internet Explorer"
            ProcessCount = $ieProcesses.Count
            TotalMemory = [math]::Round(($ieProcesses | Measure-Object WorkingSet -Sum).Sum / 1MB, 2)
            Windows = ($ieProcesses | Where-Object { $_.MainWindowTitle -ne "" }).Count
            Tabs = @()
            CPUUsage = [math]::Round(($ieProcesses | Measure-Object CPU -Sum).Sum, 2)
            Extensions = "N/A"
        }
    }

    return $browsers
}

# Função para análise de atividade de rede
function Get-NetworkActivity {
    try {
        $networkStats = Get-NetAdapterStatistics -ErrorAction SilentlyContinue
        $activeConnections = Get-NetTCPConnection -State Established -ErrorAction SilentlyContinue

        return @{
            ActiveConnections = $activeConnections.Count
            NetworkAdapters = $networkStats.Count
            TotalBytesReceived = ($networkStats | Measure-Object -Property ReceivedBytes -Sum).Sum
            TotalBytesSent = ($networkStats | Measure-Object -Property SentBytes -Sum).Sum
            Timestamp = Get-Date
        }
    }
    catch {
        return @{
            ActiveConnections = 0
            NetworkAdapters = 0
            TotalBytesReceived = 0
            TotalBytesSent = 0
            Timestamp = Get-Date
        }
    }
}

# Função para capturar screenshot (opcional)
function Capture-Screenshot {
    if (-not $SCREENSHOT_INTERVAL_MINUTES) { return }

    try {
        # Verificar se é hora de capturar screenshot
        $lastScreenshot = $global:screenshotLog | Select-Object -Last 1
        if ($lastScreenshot -and ((Get-Date) - $lastScreenshot.Timestamp).TotalMinutes -lt $SCREENSHOT_INTERVAL_MINUTES) {
            return
        }

        # Capturar screenshot usando .NET
        Add-Type -AssemblyName System.Windows.Forms
        Add-Type -AssemblyName System.Drawing

        $screen = [System.Windows.Forms.Screen]::PrimaryScreen
        $bitmap = New-Object System.Drawing.Bitmap($screen.Bounds.Width, $screen.Bounds.Height)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        $graphics.CopyFromScreen($screen.Bounds.X, $screen.Bounds.Y, 0, 0, $screen.Bounds.Size)

        # Salvar screenshot com timestamp
        $screenshotPath = "screenshot_$(Get-Date -Format 'yyyyMMdd_HHmmss').png"
        $bitmap.Save($screenshotPath, [System.Drawing.Imaging.ImageFormat]::Png)

        $global:screenshotLog += @{
            Timestamp = Get-Date
            FilePath = $screenshotPath
            Resolution = "$($screen.Bounds.Width)x$($screen.Bounds.Height)"
        }

        $graphics.Dispose()
        $bitmap.Dispose()

        Write-Host "Screenshot capturado: $screenshotPath" -ForegroundColor Green
    }
    catch {
        Write-Host "Erro ao capturar screenshot: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Função para análise de horários de atividade avançada
function Get-AdvancedActivityHours {
    $hourlyActivity = @{}
    $hourlyProductivity = @{}

    foreach ($entry in $global:activityLog) {
        $hour = $entry.Timestamp.Hour

        # Contagem de atividades
        if ($hourlyActivity.ContainsKey($hour)) {
            $hourlyActivity[$hour]++
        } else {
            $hourlyActivity[$hour] = 1
        }

        # Score de produtividade por hora
        if ($hourlyProductivity.ContainsKey($hour)) {
            $hourlyProductivity[$hour] += $entry.ProductivityScore
        } else {
            $hourlyProductivity[$hour] = $entry.ProductivityScore
        }
    }

    # Calcular médias de produtividade
    foreach ($hour in $hourlyProductivity.Keys) {
        $hourlyProductivity[$hour] = [math]::Round($hourlyProductivity[$hour] / $hourlyActivity[$hour], 2)
    }

    return @{
        ActivityCount = $hourlyActivity
        ProductivityScore = $hourlyProductivity
    }
}

# Função para gerar nome do arquivo com mais detalhes
function Get-ReportFileName {
    $username = $env:USERNAME
    $computerName = $env:COMPUTERNAME
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    return "ProductivityReport_${username}_${computerName}_${timestamp}.txt"
}

# Função para análise de eficiência
function Calculate-EfficiencyMetrics {
    $totalTime = ((Get-Date) - $global:startTime).TotalMinutes
    $activeTime = $totalTime - ($global:idleTime / 60)

    # Calcular score médio de produtividade
    $avgProductivityScore = if ($global:activityLog.Count -gt 0) {
        [math]::Round(($global:activityLog | Measure-Object -Property ProductivityScore -Average).Average, 2)
    } else { 0 }

    # Calcular eficiência de mudanças de aplicativo
    $appSwitchEfficiency = if ($totalTime -gt 0) {
        [math]::Round($global:windowSwitches.Count / $totalTime * 60, 2)  # Switches per hour
    } else { 0 }

    # Tempo em aplicativos produtivos
    $productiveTime = 0
    foreach ($app in $global:applicationUsage.Values) {
        if ($app.ProductivityScore -ge 70) {
            $productiveTime += $app.TotalTime
        }
    }

    $productivityPercentage = if ($totalTime -gt 0) {
        [math]::Round(($productiveTime / 60) / $totalTime * 100, 2)
    } else { 0 }

    return @{
        TotalTimeMinutes = [math]::Round($totalTime, 2)
        ActiveTimeMinutes = [math]::Round($activeTime, 2)
        IdleTimeMinutes = [math]::Round($global:idleTime / 60, 2)
        ActivityPercentage = if ($totalTime -gt 0) { [math]::Round($activeTime / $totalTime * 100, 2) } else { 0 }
        AverageProductivityScore = $avgProductivityScore
        AppSwitchesPerHour = $appSwitchEfficiency
        ProductiveTimePercentage = $productivityPercentage
        ClicksPerMinute = if ($activeTime -gt 0) { [math]::Round($global:mouseClicks / $activeTime, 2) } else { 0 }
        KeystrokesPerMinute = if ($activeTime -gt 0) { [math]::Round($global:keystrokes / $activeTime, 2) } else { 0 }
    }
}

# Função revolucionária para gerar relatório avançado
function Generate-AdvancedProductivityReport {
    param(
        [bool]$IsFinalReport = $false
    )

    # Coletar todas as informações avançadas
    $systemInfo = Get-AdvancedSystemInfo
    $browserInfo = Get-AdvancedBrowserInfo
    $activityHours = Get-AdvancedActivityHours
    $behaviorPatterns = Analyze-BehaviorPatterns
    $efficiencyMetrics = Calculate-EfficiencyMetrics
    $networkActivity = Get-NetworkActivity

    # Criar nome do arquivo
    $filename = Get-ReportFileName

    # Calcular estatísticas avançadas
    $totalMinutes = [math]::Round(((Get-Date) - $global:startTime).TotalMinutes, 2)
    $totalHours = [math]::Round($totalMinutes / 60, 2)

    # Top aplicativos por tempo de uso com score de produtividade
    $topApps = $global:applicationUsage.GetEnumerator() |
               Sort-Object {$_.Value.TotalTime} -Descending |
               Select-Object -First 15

    # Análise de produtividade por categoria
    $categoryStats = @{}
    foreach ($category in $PRODUCTIVITY_CATEGORIES.Keys) {
        $categoryApps = $global:applicationUsage.Values | Where-Object { $_.Category -eq $category }
        if ($categoryApps) {
            $categoryStats[$category] = @{
                TotalTime = [math]::Round(($categoryApps | Measure-Object -Property TotalTime -Sum).Sum / 60, 2)
                AppCount = $categoryApps.Count
                AvgScore = [math]::Round(($categoryApps | Measure-Object -Property ProductivityScore -Average).Average, 2)
            }
        }
    }

    # Gerar conteúdo do relatório revolucionário
    $reportType = if($IsFinalReport) { "FINAL" } else { "PARCIAL" }
    
    $report = @"
========================================
RELATÓRIO $reportType DE PRODUTIVIDADE
========================================
Gerado em: $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")

INFORMAÇÕES DO SISTEMA:
----------------------------------------
Usuário: $($systemInfo.UserName)
Computador: $($systemInfo.ComputerName)
Domínio: $($systemInfo.Domain)
Sistema Operacional: $($systemInfo.OS)
RAM Total: $($systemInfo.TotalRAM) GB
Processador: $($systemInfo.Processor)
Executando como Admin: $($systemInfo.IsAdmin)

PERÍODO DE MONITORAMENTO:
----------------------------------------
Início: $($systemInfo.MonitoringStart.ToString("dd/MM/yyyy HH:mm:ss"))
Fim: $($systemInfo.MonitoringEnd.ToString("dd/MM/yyyy HH:mm:ss"))
Duração Total: $totalHours horas ($totalMinutes minutos)

ESTATÍSTICAS GERAIS:
----------------------------------------
Total de Cliques Estimados: $global:mouseClicks
Cliques por Minuto: $clicksPerMinute
Total de Mudanças de Janela: $($global:activityLog.Count)
Horário de Maior Atividade: $peakHour`:00
Aplicativos Únicos Utilizados: $($global:applicationUsage.Count)
Janelas Únicas Focadas: $($global:activeWindows.Count)

========================================
TOP 10 APLICATIVOS POR TEMPO DE USO:
========================================
"@

    $rank = 1
    foreach ($app in $topApps) {
        $timeMinutes = [math]::Round($app.Value.TotalTime / 60, 2)
        $percentage = if($totalMinutes -gt 0) { [math]::Round(($timeMinutes / $totalMinutes) * 100, 1) } else { 0 }
        
        $report += "`n$rank. $($app.Value.ProcessName)"
        $report += "`n   Tempo de Uso: $timeMinutes minutos ($percentage%)"
        $report += "`n   Primeiro Uso: $($app.Value.FirstUsed.ToString("HH:mm:ss"))"
        $report += "`n   Último Uso: $($app.Value.LastUsed.ToString("HH:mm:ss"))"
        $report += "`n"
        $rank++
    }

    $report += @"

========================================
NAVEGADORES DETECTADOS:
========================================
"@

    if ($browserInfo.Count -gt 0) {
        foreach ($browser in $browserInfo) {
            $report += "`n$($browser.Browser):"
            $report += "`n   Processos Ativos: $($browser.ProcessCount)"
            $report += "`n   Janelas Abertas: $($browser.Windows)"
            $report += "`n   Memória Total: $($browser.TotalMemory) MB"
            $report += "`n"
        }
    } else {
        $report += "`nNenhum navegador detectado no momento da coleta.`n"
    }

    $report += @"

========================================
ATIVIDADE POR HORÁRIO:
========================================
"@

    if ($activityHours.Count -gt 0) {
        foreach ($hour in $activityHours.GetEnumerator() | Sort-Object Name) {
            $percentage = [math]::Round(($hour.Value / $global:activityLog.Count) * 100, 1)
            $report += "`n$($hour.Name.ToString("00")):00 - $($hour.Value) atividades ($percentage%)"
        }
    } else {
        $report += "`nNenhuma atividade registrada por horário."
    }

    $report += @"


========================================
JANELAS MAIS UTILIZADAS (TOP 15):
========================================
"@

    $topWindows = $global:activeWindows.GetEnumerator() | Sort-Object {$_.Value.TotalSeconds} -Descending | Select-Object -First 15
    $rank = 1
    
    foreach ($window in $topWindows) {
        $timeMinutes = [math]::Round($window.Value.TotalSeconds / 60, 2)
        $percentage = if($totalMinutes -gt 0) { [math]::Round(($timeMinutes / ($totalMinutes * 60)) * 100, 1) } else { 0 }
        
        $report += "`n$rank. $($window.Value.ProcessName)"
        $report += "`n   Janela: $($window.Value.WindowTitle)"
        $report += "`n   Tempo Focada: $timeMinutes minutos"
        $report += "`n   Vezes Focada: $($window.Value.Count)"
        $report += "`n   Primeiro Foco: $($window.Value.FirstSeen.ToString("HH:mm:ss"))"
        $report += "`n   Último Foco: $($window.Value.LastSeen.ToString("HH:mm:ss"))"
        $report += "`n"
        $rank++
    }

    $report += @"

========================================
PROCESSOS ATIVOS (TOP 20 POR CPU):
========================================
"@

    try {
        $processes = Get-Process | Where-Object {
            $_.ProcessName -notlike "svchost*" -and 
            $_.ProcessName -notlike "dwm*" -and
            $_.ProcessName -notlike "csrss*" -and
            $_.WorkingSet -gt 10MB
        } | Sort-Object CPU -Descending | Select-Object -First 20

        foreach ($process in $processes) {
            $cpuTime = if($process.CPU) { [math]::Round($process.CPU, 2) } else { 0 }
            $memory = [math]::Round($process.WorkingSet / 1MB, 2)
            
            $report += "`n- $($process.ProcessName) (PID: $($process.Id))"
            $report += "`n  CPU: ${cpuTime}s | Memória: ${memory} MB"
            if ($process.MainWindowTitle -ne "") {
                $report += "`n  Janela: $($process.MainWindowTitle)"
            }
            $report += "`n"
        }
    }
    catch {
        $report += "`nErro ao coletar informações de processos: $($_.Exception.Message)"
    }

    $report += @"

========================================
INFORMAÇÕES TÉCNICAS:
========================================
- Intervalo de Coleta: $SAMPLE_INTERVAL_SECONDS segundos
- Intervalo de Salvamento: $SAVE_INTERVAL_MINUTES minutos
- Cliques estimados por atividade de janela
- Relatório Automático: $(-not $IsFinalReport)
- Executado com Privilégios Admin: $isAdmin

========================================
OBSERVAÇÕES IMPORTANTES:
========================================
- Dados coletados apenas durante o período de monitoramento
- Cliques de mouse são estimativas baseadas em atividade
- Tempos calculados com base em foco de janelas
- Aplicativos em segundo plano podem não ser totalmente registrados
- Este relatório é gerado automaticamente para análise de produtividade

Relatório gerado pelo Sistema de Monitoramento de Produtividade
Data/Hora: $(Get-Date -Format "dd/MM/yyyy HH:mm:ss")
========================================
"@

    # Salvar o relatório
    try {
        $report | Out-File -FilePath $filename -Encoding UTF8 -Force
        
        $action = if($IsFinalReport) { "FINAL" } else { "atualizado" }
        Write-Host "[$((Get-Date).ToString("HH:mm:ss"))] Relatório $action salvo: $filename" -ForegroundColor Green
        
        # Log do tamanho do arquivo
        $fileSize = [math]::Round((Get-Item $filename).Length / 1KB, 2)
        Write-Host "[$((Get-Date).ToString("HH:mm:ss"))] Tamanho do arquivo: $fileSize KB" -ForegroundColor Gray
        
        return $filename
    }
    catch {
        Write-Host "[$((Get-Date).ToString("HH:mm:ss"))] ERRO ao salvar relatório: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Função principal de monitoramento contínuo
function Start-ContinuousMonitoring {
    Write-Host "=== MONITOR DE PRODUTIVIDADE CONTÍNUO ===" -ForegroundColor Cyan
    Write-Host "Usuário: $env:USERNAME" -ForegroundColor Yellow
    Write-Host "Computador: $env:COMPUTERNAME" -ForegroundColor Yellow
    Write-Host "Iniciado em: $($global:startTime.ToString("dd/MM/yyyy HH:mm:ss"))" -ForegroundColor Yellow
    Write-Host "Intervalo de coleta: $SAMPLE_INTERVAL_SECONDS segundos" -ForegroundColor Gray
    Write-Host "Salvamento automático: a cada $SAVE_INTERVAL_MINUTES minutos" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Pressione Ctrl+C para parar e gerar relatório final..." -ForegroundColor White
    Write-Host "O monitoramento continuará até ser interrompido ou o sistema desligar." -ForegroundColor White
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    $global:lastSaveTime = Get-Date
    $sampleCount = 0
    
    # Handler para Ctrl+C
    Register-EngineEvent PowerShell.Exiting -Action {
        $global:isRunning = $false
        Write-Host "`n[$(Get-Date -Format "HH:mm:ss")] Finalizando monitoramento..." -ForegroundColor Yellow
        Generate-ProductivityReport -IsFinalReport $true
        Write-Host "Monitoramento finalizado." -ForegroundColor Green
    }
    
    try {
        while ($global:isRunning) {
            $currentTime = Get-Date
            
            try {
                # Coletar dados de atividade
                Log-WindowActivity
                $sampleCount++
                
                # Mostrar progresso a cada 60 amostras (aproximadamente 3 minutos)
                if ($sampleCount % 60 -eq 0) {
                    $runtime = [math]::Round(($currentTime - $global:startTime).TotalMinutes, 1)
                    $nextSave = [math]::Round($SAVE_INTERVAL_MINUTES - ($currentTime - $global:lastSaveTime).TotalMinutes, 1)
                    
                    Write-Host "[$($currentTime.ToString("HH:mm:ss"))] Ativo há $runtime min | Próximo save em $nextSave min | Amostras: $sampleCount" -ForegroundColor Gray
                }
                
                # Salvar relatório periódico
                if (($currentTime - $global:lastSaveTime).TotalMinutes -ge $SAVE_INTERVAL_MINUTES) {
                    Generate-ProductivityReport -IsFinalReport $false
                    $global:lastSaveTime = $currentTime
                }
                
                Start-Sleep -Seconds $SAMPLE_INTERVAL_SECONDS
            }
            catch {
                Write-Host "[$($currentTime.ToString("HH:mm:ss"))] Erro na coleta: $($_.Exception.Message)" -ForegroundColor Red
                Start-Sleep -Seconds ($SAMPLE_INTERVAL_SECONDS * 2)  # Esperar mais tempo em caso de erro
            }
        }
    }
    catch {
        Write-Host "Erro crítico no monitoramento: $($_.Exception.Message)" -ForegroundColor Red
    }
    finally {
        # Gerar relatório final
        Write-Host "`nGerando relatório final..." -ForegroundColor Yellow
        $finalReport = Generate-ProductivityReport -IsFinalReport $true
        
        if ($finalReport) {
            Write-Host "Relatório final salvo: $finalReport" -ForegroundColor Green
            Write-Host "Localização completa: $(Get-Location)\$finalReport" -ForegroundColor Yellow
        }
        
        $totalTime = [math]::Round(((Get-Date) - $global:startTime).TotalHours, 2)
        Write-Host "Monitoramento executado por $totalTime horas." -ForegroundColor Cyan
        Write-Host "Total de amostras coletadas: $sampleCount" -ForegroundColor Cyan
    }
}

# Execução principal sem confirmação (para deploy em empresa)
try {
    Start-ContinuousMonitoring
}
catch {
    Write-Host "Erro fatal: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Tentando gerar relatório de emergência..." -ForegroundColor Yellow
    
    try {
        Generate-ProductivityReport -IsFinalReport $true
    }
    catch {
        Write-Host "Não foi possível gerar relatório de emergência." -ForegroundColor Red
    }
    
    exit 1
}