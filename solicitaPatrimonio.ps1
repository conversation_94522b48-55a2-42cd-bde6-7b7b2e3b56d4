Add-Type -AssemblyName PresentationFramework
Add-Type -AssemblyName PresentationCore
Add-Type -AssemblyName WindowsBase
Add-Type -AssemblyName System.Drawing

# Paleta de cores da marca
$colorPrimary = "#43097D"
$colorSecondary = "#8E56EE"
$colorAccent1 = "#097D71"
$colorAccent2 = "#E605FA"
$colorAccent3 = "#F27629"

# Verificar se já existe patrimônio registrado
$registryPath = "HKCU:\Software\Onix"
$patrimonioRegistrado = $null

# Verifica se a chave existe e se a propriedade 'Patrimonio' existe
if (Test-Path $registryPath) {
    $properties = Get-ItemProperty -Path $registryPath
    if ($null -ne $properties.PSObject.Properties['Patrimonio']) {
        $patrimonioRegistrado = $properties.Patrimonio
    }
}

# Se já estiver registrado e for válido, não mostra a janela
if ($patrimonioRegistrado -match '^\d{11}$') {
    return
}

# XAML com design moderno e dinâmico
[xml]$xaml = @"
<Window 
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Title="Identificação de Patrimônio" 
    Height="380" 
    Width="600"
    WindowStartupLocation="CenterScreen" 
    ResizeMode="NoResize"
    Topmost="True"
    Background="Transparent"
    WindowStyle="None"
    AllowsTransparency="True">
    
    <Border BorderBrush="$colorSecondary" BorderThickness="2" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect BlurRadius="20" ShadowDepth="0" Color="#FF8E56EE" Opacity="0.8"/>
        </Border.Effect>
        
        <Grid>
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#FF1A1A1A" Offset="0"/>
                    <GradientStop Color="#FF0D0D0D" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>
            
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Cabeçalho com gradiente -->
            <Border Grid.Row="0" CornerRadius="13,13,0,0" Height="80">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="$colorPrimary" Offset="0.0"/>
                        <GradientStop Color="$colorSecondary" Offset="1.0"/>
                    </LinearGradientBrush>
                </Border.Background>
                <TextBlock 
                    Text="IDENTIFICAÇÃO DE PATRIMÔNIO"
                    Foreground="White"
                    FontSize="20"
                    FontWeight="Bold"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    TextOptions.TextRenderingMode="ClearType"
                    RenderOptions.BitmapScalingMode="HighQuality">
                    <TextBlock.Effect>
                        <DropShadowEffect BlurRadius="5" ShadowDepth="0" Color="White" Opacity="0.3"/>
                    </TextBlock.Effect>
                </TextBlock>
            </Border>
            
            <!-- Conteúdo Principal -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Ícone animado -->
                <Viewbox Grid.Row="0" Width="80" Height="80" HorizontalAlignment="Center" Margin="0,0,0,15">
                    <Canvas Width="24" Height="24">
                        <Path 
                            Fill="$colorAccent3" 
                            Data="M12,3L1,9L12,15L21,10.09V17H23V9M5,13.36V17L12,21L19,17V13.36L12,17L5,13.36Z">
                            <Path.RenderTransform>
                                <RotateTransform CenterX="12" CenterY="12" Angle="0"/>
                            </Path.RenderTransform>
                            <Path.Triggers>
                                <EventTrigger RoutedEvent="Window.Loaded">
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation 
                                                Storyboard.TargetProperty="RenderTransform.Angle"
                                                From="0" To="5" Duration="0:0:0.5" AutoReverse="True" RepeatBehavior="Forever"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Path.Triggers>
                        </Path>
                    </Canvas>
                </Viewbox>
                
                <!-- Instruções -->
                <TextBlock 
                    Grid.Row="1"
                    Text="Por favor, informe o número de patrimônio do computador:"
                    Foreground="#CCCCCC"
                    FontSize="16"
                    HorizontalAlignment="Center"
                    Margin="0,0,0,15"
                    TextWrapping="Wrap"/>
                
                <!-- Campo de entrada com animação -->
                <Border 
                    Grid.Row="2"
                    Background="#FF1A1A1A"
                    CornerRadius="8"
                    BorderThickness="2"
                    HorizontalAlignment="Center"
                    Width="350"
                    Height="55">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="BorderBrush" Value="#FF444444"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding ElementName=txtPatrimonio, Path=IsFocused}" Value="True">
                                    <Setter Property="BorderBrush" Value="$colorAccent2"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    
                    <TextBox 
                        Name="txtPatrimonio"
                        Background="Transparent"
                        BorderThickness="0"
                        Foreground="White"
                        FontSize="20"
                        FontWeight="Bold"
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"
                        CaretBrush="$colorAccent2"
                        MaxLength="11">
                        <TextBox.Effect>
                            <DropShadowEffect BlurRadius="10" ShadowDepth="0" Color="#FF8E56EE" Opacity="0.4"/>
                        </TextBox.Effect>
                    </TextBox>
                </Border>
                
                <!-- Contador de dígitos -->
                <TextBlock 
                    Grid.Row="3"
                    Name="lblCounter"
                    Text="0/11"
                    Foreground="#666666"
                    FontSize="13"
                    HorizontalAlignment="Center"
                    Margin="0,5,0,0"/>
                
                <!-- Mensagem de erro -->
                <Border 
                    Grid.Row="3"
                    Name="errorPanel"
                    Background="#33000000"
                    CornerRadius="5"
                    Padding="10,5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Bottom"
                    Margin="0,30,0,0"
                    Visibility="Collapsed">
                    <TextBlock 
                        Name="lblError"
                        Foreground="$colorAccent3"
                        FontSize="14"
                        FontWeight="Bold"
                        TextWrapping="Wrap"
                        TextAlignment="Center"/>
                </Border>
            </Grid>
            
            <!-- Rodapé com botões -->
            <Grid Grid.Row="2" Margin="0,15,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button 
                    Grid.Column="1"
                    Name="btnOK" 
                    Content="CONFIRMAR"
                    Width="160"
                    Height="45"
                    FontSize="16"
                    FontWeight="Bold"
                    Margin="0,0,10,0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="$colorPrimary"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Cursor" Value="Hand"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border 
                                            Background="{TemplateBinding Background}"
                                            CornerRadius="8"
                                            Opacity="0.9">
                                            <ContentPresenter 
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="1"/>
                                    <Setter Property="Background" Value="$colorSecondary"/>
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect BlurRadius="10" ShadowDepth="0" Color="$colorSecondary" Opacity="0.7"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                                <DataTrigger Binding="{Binding ElementName=txtPatrimonio, Path=Text.Length}" Value="11">
                                    <Setter Property="IsEnabled" Value="True"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding ElementName=txtPatrimonio, Path=Text.Length}" Value="0">
                                    <Setter Property="IsEnabled" Value="False"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
                
                <Button 
                    Grid.Column="2"
                    Name="btnCancel" 
                    Content="FECHAR"
                    Width="130"
                    Height="45"
                    FontSize="15"
                    FontWeight="Bold">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="Foreground" Value="#AAAAAA"/>
                            <Setter Property="BorderBrush" Value="#FF444444"/>
                            <Setter Property="BorderThickness" Value="1"/>
                            <Setter Property="Cursor" Value="Hand"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border 
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="8"
                                            Opacity="0.8">
                                            <ContentPresenter 
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="1"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="BorderBrush" Value="$colorAccent3"/>
                                    <Setter Property="Effect">
                                        <Setter.Value>
                                            <DropShadowEffect BlurRadius="10" ShadowDepth="0" Color="$colorAccent3" Opacity="0.4"/>
                                        </Setter.Value>
                                    </Setter>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Grid>
    </Border>
</Window>
"@

# Carregar o XAML
$reader = (New-Object System.Xml.XmlNodeReader $xaml)
$window = [Windows.Markup.XamlReader]::Load($reader)
$txtPatrimonio = $window.FindName("txtPatrimonio")
$btnOK = $window.FindName("btnOK")
$btnCancel = $window.FindName("btnCancel")
$lblError = $window.FindName("lblError")
$lblCounter = $window.FindName("lblCounter")
$errorPanel = $window.FindName("errorPanel")

# Inicialmente desabilitar o botão Confirmar
$btnOK.IsEnabled = $false

# Validar entrada em tempo real
$txtPatrimonio.Add_TextChanged({
    $errorPanel.Visibility = "Collapsed"
    $currentText = $txtPatrimonio.Text
    
    # Atualizar contador
    $lblCounter.Text = "$($currentText.Length)/11"
    $lblCounter.Foreground = if ($currentText.Length -eq 11) { $colorAccent1 } else { "#666666" }
    
    # Remover caracteres não numéricos
    if ($currentText -match '[^\d]') {
        $txtPatrimonio.Text = $currentText -replace '[^\d]', ''
        $txtPatrimonio.CaretIndex = $txtPatrimonio.Text.Length
    }
    
    # Atualizar botão Confirmar
    $btnOK.IsEnabled = ($txtPatrimonio.Text.Length -eq 11)
})

# Lógica do botão Confirmar
$btnOK.Add_Click({
    $patrimonio = $txtPatrimonio.Text.Trim()
    
    if ($patrimonio.Length -ne 11) {
        $lblError.Text = "O patrimônio deve conter exatamente 11 dígitos!"
        $errorPanel.Visibility = "Visible"
        return
    }
    
    # Criar chave no registro se necessário
    $registryPath = "HKCU:\Software\Onix"
    if (-not (Test-Path $registryPath)) {
        New-Item -Path $registryPath -Force | Out-Null
    }
    
    # Salvar no registro
    Set-ItemProperty -Path $registryPath -Name "Patrimonio" -Value $patrimonio -Force
    
    $window.Close()
})

# Lógica do botão Fechar
$btnCancel.Add_Click({
    # Forçar o usuário a preencher o patrimônio
    $lblError.Text = "Você deve informar o número de patrimônio para continuar usando o computador."
    $errorPanel.Visibility = "Visible"
    
    # Animação de shake no painel de erro
    $transform = New-Object Windows.Media.TranslateTransform
    $errorPanel.RenderTransform = $transform
    $animation = New-Object Windows.Media.Animation.DoubleAnimation
    $animation.From = 0
    $animation.To = 10
    $animation.Duration = New-Object Windows.Duration(TimeSpan.FromMilliseconds(50))
    $animation.AutoReverse = $true
    $animation.RepeatBehavior = [Windows.Media.Animation.RepeatBehavior]::Forever
    $transform.BeginAnimation([Windows.Media.TranslateTransform]::XProperty, $animation)
    
    # Parar a animação após 0.5 segundos
    Start-Sleep -Milliseconds 500
    $transform.BeginAnimation([Windows.Media.TranslateTransform]::XProperty, $null)
    $transform.X = 0
})

# Focar no campo ao iniciar
$window.Add_Loaded({
    $txtPatrimonio.Focus()
})

# Exibir a janela
$window.ShowDialog() | Out-Null